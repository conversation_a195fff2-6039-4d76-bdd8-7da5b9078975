@Override
public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
    if (holder instanceof SubtitleViewHolder) {
        SubtitleViewHolder viewHolder = (SubtitleViewHolder) holder;
        ConvertContentItem item = mData.get(position);
        viewHolder.bind(item);
        
        // 为最后一个项添加渐变动画
        if (position == mData.size() - 1) {
            applyFadeInAnimation(viewHolder.itemView);
        }
    }
}

// 添加渐变动画效果
private void applyFadeInAnimation(View view) {
    // 创建渐变动画
    AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
    fadeIn.setDuration(500);
    fadeIn.setFillAfter(true);
    view.startAnimation(fadeIn);
}