// 添加阴影效果方法
private void setupShadow() {
    // 设置阴影参数：x=0, y=14, Blur=18, Spread=0, 颜色=#DB38C
    float shadowDx = 0;
    float shadowDy = 14;
    float shadowRadius = 18;
    int shadowColor = Color.parseColor("#DB38C3");
    
    // 应用阴影效果
    setLayerType(LAYER_TYPE_SOFTWARE, null);
    Paint shadowPaint = getPaint();
    shadowPaint.setShadowLayer(shadowRadius, shadowDx, shadowDy, shadowColor);
}

@Override
protected void onFinishInflate() {
    super.onFinishInflate();
    setupShadow();
}