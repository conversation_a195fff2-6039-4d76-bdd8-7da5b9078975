<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/recorder_header"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/recorder_background_gradient"
        android:tag="sharedView"/>

    <!--该View仅用来适配底部 taskbar 颜色适配，若后续有新的方案可处理，可删除该View-->
    <View
        android:id="@+id/view_task_bar_navigation"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@color/background_taskbar_navigation"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/coui_color_background_with_card">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/abl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/coui_transparence"
            app:elevation="0dp"
            app:layout_constraintTop_toTopOf="parent">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                android:tag="toolBar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp52" />

        </com.google.android.material.appbar.AppBarLayout>

        <com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
            android:id="@+id/wave_gradient_view"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:tag="waveGradientView"
            app:layout_constraintHeight_percent="@dimen/record_wave_view_height_percent"
            android:layout_marginTop="@dimen/common_wave_view_margin_top"
            app:backgroundWhole="@color/wave_recycler_background"
            app:layout_constraintTop_toBottomOf="@id/recorder_top">

            <com.soundrecorder.record.views.wave.RecorderWaveRecyclerView
                android:id="@+id/ruler_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:layoutDirection="ltr"
                android:tag="WaveRecyclerView" />
        </com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout>

        <!--实时字幕区域-->
        <LinearLayout
            android:id="@+id/real_time_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="invisible"
            android:layout_marginTop="@dimen/realtime_view_margin_top"
            android:orientation="vertical"
            android:layout_marginStart="@dimen/real_time_view_margin"
            android:layout_marginEnd="@dimen/real_time_view_margin"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/markListViewBottom"
            app:layout_constraintTop_toBottomOf="@id/recorder_top">
            <TextView
                android:id="@+id/select_language_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dp28"
                android:paddingStart="@dimen/dp9"
                android:paddingTop="@dimen/dp6"
                android:paddingEnd="@dimen/dp9"
                android:paddingBottom="@dimen/dp6"
                android:layout_marginBottom="@dimen/dp16"
                android:background="@drawable/select_bg"
                android:lines="1"
                android:ellipsize="end"
                style="@style/couiTextBodyXS"
                android:gravity="center"
                android:includeFontPadding="false"
                android:drawablePadding="@dimen/dp4"
                android:drawableStart="@drawable/ic_language_browser"
                android:drawableEnd="@drawable/ic_polygon"
                android:textColor="?attr/couiColorLabelPrimary"
                android:fontFamily="sans-serif-medium"
                android:textFontWeight="500"
                android:forceDarkAllowed="false" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/captions_gradient_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:tag="CaptionsGradientView"
                app:backgroundWhole="@color/wave_recycler_background">

                <androidx.recyclerview.widget.COUIRecyclerView
                    android:id="@+id/captions_view"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:scrollbars="vertical"
                    android:scrollbarThumbVertical="@drawable/scroll_bar_indicative"
                    android:scrollbarTrackVertical="@drawable/scroll_track"
                    android:fadeScrollbars="false"
                    tools:listitem="@layout/item_data"
                    android:tag="CaptionsRecyclerView" />

                <LinearLayout
                    android:id="@+id/captions_loading_layout"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/captions_text_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/dp16"
                        android:textFontWeight="400"
                        android:textColor="@color/enable_recorde_button_text_color"
                        android:fontFamily="sans-serif-medium"
                        android:layout_gravity="center"
                        android:text="@string/subtitle_identify"/>

                    <com.oplus.anim.EffectiveAnimationView
                        android:id="@+id/captions_animation_view"
                        android:layout_width="@dimen/dp25"
                        android:layout_height="@dimen/dp20"
                        android:layout_gravity="center"
                        app:anim_repeatMode="reverse" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_start"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="@dimen/responsive_ui_margin_large" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_end"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_end="@dimen/responsive_ui_margin_large" />


        <FrameLayout
            android:id="@+id/middle_control"
            android:layout_width="@dimen/circle_record_button_diam"
            android:layout_height="@dimen/circle_record_button_diam"
            android:gravity="center"
            android:layout_marginBottom="@dimen/recorder_bottom_button_margin"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/guideline_end"
            app:layout_constraintStart_toStartOf="@id/guideline_start">

            <ImageView
                android:id="@+id/recorder_save"
                android:layout_width="@dimen/dp56"
                android:layout_height="@dimen/dp56"
                android:contentDescription="@string/rename_save"
                android:tag="sharedRedCircleView"
                android:src="@drawable/ic_record_stop" />

        </FrameLayout>

        <LinearLayout
            android:id="@+id/left_mark_layout"
            android:orientation="vertical"
            android:layout_width="@dimen/record_button_width"
            android:gravity="end"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/recorder_bottom_button_margin"
            android:tag="markView"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintEnd_toStartOf="@id/middle_control"
            app:layout_constraintTop_toTopOf="@id/middle_control">

            <com.soundrecorder.record.views.CustomButtonView
                android:id="@+id/left_mark_control"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:background="@null"
                android:contentDescription="@string/talkback_flag"
                android:forceDarkAllowed="false"
                app:button_enable="true"
                app:button_light_img="@drawable/selector_button_mark"
                app:button_light_text_color="@color/coui_color_label_secondary"
                app:button_text="@string/talkback_flag"
                app:button_un_light_img="@drawable/selector_button_mark_unable"
                app:button_un_light_text_color="@color/coui_color_label_secondary" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ib_mark_photo_layout"
            android:layout_width="wrap_content"
            android:orientation="vertical"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/recorder_bottom_button_margin"
            android:tag="stopView"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintStart_toEndOf="@id/middle_control"
            app:layout_constraintTop_toTopOf="@id/middle_control">

            <com.soundrecorder.record.views.RecorderAnimatedCircleButton
                android:id="@+id/red_circle_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:forceDarkAllowed="false"
                app:circle_radius="@dimen/dp24"
                android:src="@drawable/ic_pause"
                app:circle_color="@color/coui_color_card" />
        </LinearLayout>

        <Space
            android:id="@+id/markListViewBottom"
            app:layout_constraintBottom_toTopOf="@id/transcription_box"
            android:layout_width="match_parent"
            android:layout_height="@dimen/record_mark_list_margin_bottom" />

        <Space
            android:id="@+id/view_center_divider"
            android:layout_width="1px"
            android:layout_height="@dimen/dp60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/recorder_top"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:tag="timerView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginHorizontal="@dimen/common_time_area_margin_horizontal"
            app:layout_constraintTop_toBottomOf="@id/abl">

            <TextView
                android:id="@+id/timerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dp66"
                android:textSize="@dimen/common_max_time_text_size"
                android:lines="1"
                android:gravity="center"
                android:padding="0dp"
                android:textColor="@color/coui_color_primary_neutral"
                android:textAppearance="@style/couiTextAppearanceDisplayM"
                android:fontFamily="sans-serif-medium"
                android:fontFeatureSettings="tnum"
                android:textFontWeight="600"
                tools:text="00:11:22" />

            <TextView
                android:id="@+id/modeTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dp22"
                android:gravity="center"
                android:textColor="@color/coui_color_label_secondary"
                android:textSize="@dimen/dp16"
                android:textFontWeight="400"
                android:fontFamily="sans-serif-medium"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/transcription_box"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            app:layout_constraintBottom_toTopOf="@id/middle_control"
            app:layout_constraintEnd_toEndOf="@id/middle_control"
            app:layout_constraintStart_toStartOf="@id/middle_control"
            android:layout_marginBottom="@dimen/dp24">

            <TextView
                android:id="@+id/transcription_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dp36"
                android:background="@drawable/transcription_bg"
                android:drawableStart="@drawable/ic_transcription_close"
                android:drawablePadding="@dimen/dp6"
                android:forceDarkAllowed="false"
                android:gravity="center"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:maxLines="1"
                android:text="@string/turn_on_transcription"
                android:textFontWeight="500"
                android:textColor="@color/coui_color_label_primary"
                android:visibility="invisible"
                android:textSize="@dimen/dp14" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>