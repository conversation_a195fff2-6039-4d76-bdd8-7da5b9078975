/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SafeArrayMapTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/1/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.center.filechange

import android.os.Build
import androidx.collection.SimpleArrayMap
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class SafeArrayMapTest {

    @Test
    fun should_equals_when_put() {
        val intArray = SafeArrayMap<String, Int>()
        intArray["1"] = 2
        Assert.assertEquals(1, intArray.size)

        val simpleArray = SimpleArrayMap<String, Int>()
        for (i in 0..10) {
            simpleArray.put(i.toString(), i)
        }
        intArray.putAll(simpleArray)
        Assert.assertEquals(11, intArray.size)

        val result = intArray["1"]
        Assert.assertEquals(1, result)
    }

    @Test
    fun should_equals_when_clear() {
        val strArray = SafeArrayMap<String, String>()
        val simpleArray = SimpleArrayMap<String, String>()
        for (i in 0..10) {
            simpleArray.put(i.toString(), i.toString())
        }
        strArray.putAll(simpleArray)
        Assert.assertEquals(11, strArray.size)
        strArray.clear()
        Assert.assertEquals(0, strArray.size)

        strArray["1"] = "2"
        var remove = listOf("3")
        var result = strArray.removeAll(remove)
        Assert.assertFalse(result)
        Assert.assertEquals(1, strArray.size)

        remove = listOf("1", "2")
        result = strArray.removeAll(remove)
        Assert.assertTrue(result)
        Assert.assertEquals(0, strArray.size)
    }
}