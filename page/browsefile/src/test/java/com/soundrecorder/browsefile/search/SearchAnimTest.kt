/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SearchAnimTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SearchAnimTest {
    private var context: Context? = null
    private var mockedStaticSdkApi: MockedStatic<SearchAnim>? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mockedStaticSdkApi = Mockito.mockStatic(SearchAnim::class.java)
    }

    @After
    fun tearDown() {
        context = null
        mockedStaticSdkApi?.close()
    }

    @Test
    fun should_notNull_when_init() {
        val searchFragment = Mockito.mock(SearchFragment::class.java)
        val searchAnim = SearchAnim(searchFragment)
        val mSearchFragment = Whitebox.getInternalState<SearchFragment>(searchAnim, "mSearchFragment")
        Assert.assertNotNull(mSearchFragment)
    }

    @Test
    fun should_notNull_when_isSubTitleInAnimation() {
        val searchAnim = Mockito.mock(SearchAnim::class.java)
        val isSub = searchAnim.isSubTitleInAnimation()
        Assert.assertNotNull(isSub)
    }

    @Test
    fun should_null_when_animateSearchOut() {
        val searchAnim = Mockito.mock(SearchAnim::class.java)
        searchAnim.animateSearchOut(false)
        val mSearchFragment = Whitebox.getInternalState<SearchFragment>(searchAnim, "mSearchFragment")
        Assert.assertNull(mSearchFragment)
    }

    @Test
    fun should_invoke_when_animateSearchIn() {
        val searchAnim = Mockito.mock(SearchAnim::class.java)
        searchAnim.animateSearchIn()
        mockedStaticSdkApi?.verify(
            { searchAnim.animateSearchIn() },
            Mockito.times(1)
        )
    }
}