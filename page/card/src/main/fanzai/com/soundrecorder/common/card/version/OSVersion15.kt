/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  OSVersion15
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/8/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.version

class OSVersion15 : OSVersion {
    @Volatile
    private var isShowStatusBarCard: Boolean = false

    /**
     * 14及以下方案通过卡片Size来控制
     */
    override fun onSeedlingCardSizeChanged(newSize: Int) {
        // do nothing
    }

    /**
     * 15及以上方案通过点击卡片状态来控制
     */
    override fun onSeedlingCardShowChangeWhenSave(showStatusBar: Boolean) {
        isShowStatusBarCard = showStatusBar
    }

    override fun resetShowStatusBar() {
        isShowStatusBarCard = false
    }

    override fun getShowStatusBar(): Boolean {
        return isShowStatusBarCard
    }

    override fun needHideSeedlingCard(): Boolean {
        return !isShowStatusBarCard
    }
}