/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlaybackContainerFragment
 * Description:
 * Version: 1.0
 * Date: 2022/11/4
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/11/4 1.0 create
 */

package com.soundrecorder.playback

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.DialogInterface.OnDismissListener
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.database.Cursor
import android.media.AudioManager
import android.net.Uri
import android.os.Bundle
import android.os.FileObserver
import android.os.Looper
import android.provider.MediaStore
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.accessibility.AccessibilityEvent
import android.widget.AdapterView
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.panel.COUIBottomSheetBehavior
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.poplist.PopupListItem.Builder
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.seekbar.COUISeekBar
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.ext.currentHasHourFormatTimeExclusive
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.ext.durationHasHourFormatTimeExclusive
import com.soundrecorder.base.ext.findFragment
import com.soundrecorder.base.ext.getValueWithDefault
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.removeFragment
import com.soundrecorder.base.ext.replaceFragmentByTag
import com.soundrecorder.base.userchange.OnFragmentUserChangeListener
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.base.utils.RecorderTextUtils
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.base.view.DeDuplicateInsetsCallback
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.buryingpoint.CuttingStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.common.dialog.PositiveCallback
import com.soundrecorder.common.fileobserve.MultiFileObserver
import com.soundrecorder.common.fileobserve.OnFileEventListener
import com.soundrecorder.common.fileoperator.delete.DeleteFileDialog
import com.soundrecorder.common.fileoperator.delete.OnFileDeleteListener
import com.soundrecorder.common.fileoperator.recover.OnRecoverFileListener
import com.soundrecorder.common.fileoperator.recover.RecoverFileDialog
import com.soundrecorder.common.fileoperator.rename.RenameFileDialog
import com.soundrecorder.common.flexible.FollowDialogRestoreUtils
import com.soundrecorder.common.permission.PermissionDialogUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.share.ShareTypeNote
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.common.utils.SendSetUtil
import com.soundrecorder.common.utils.TimeSetUtils
import com.soundrecorder.common.utils.VibrateUtils
import com.soundrecorder.common.utils.invisible
import com.soundrecorder.common.utils.sound.DeleteSoundEffectManager
import com.soundrecorder.common.utils.visible
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.EditRecordInterface
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.mark.IIPictureMarkListener
import com.soundrecorder.modulerouter.mark.IPictureMarkDelegate
import com.soundrecorder.modulerouter.mark.IPictureMarkLifeOwnerProvider
import com.soundrecorder.modulerouter.mark.WaveMarkInterface
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_CONVERT_STATUS
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_RECORD_ID
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_NAME_TEXT
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_STATUS
import com.soundrecorder.modulerouter.playback.NOTIFY_CONVERT_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.NOTIFY_SMART_NAME_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.PAGE_FROM_PLAYBACK
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.modulerouter.smartname.ISmartNameManager
import com.soundrecorder.modulerouter.summary.BUNDLE_MEDIA_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_FILE_DURATION
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_FILE_PATH
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_MODIFY_TIME
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_TITLE
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_TYPE
import com.soundrecorder.modulerouter.summary.IAISummaryCallback
import com.soundrecorder.modulerouter.summary.IAISummaryInterface
import com.soundrecorder.modulerouter.summary.IImmersiveCallback
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_ADD
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_DELETE
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_INIT
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_RENAME
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.TAB_INDEX_FIRST
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.TAB_INDEX_SECOND
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.TAB_TYPE_CONVERT
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.TAB_TYPE_SUMMARY
import com.soundrecorder.playback.audio.PlaybackAudioFragment
import com.soundrecorder.playback.audio.setting.PlaySettingDialogFragment
import com.soundrecorder.playback.convert.IConvertManager
import com.soundrecorder.playback.databinding.FragmentPlaybackContainerBinding
import com.soundrecorder.playback.newconvert.PlaybackConvertFragment
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_CANCEL
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_COMPLETE
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_INIT
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_PROGRESS
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_SUMMARY_NONE_COMPLETE
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_USERTIMEOUT
import com.soundrecorder.playback.newconvert.convert.ConvertManagerImpl
import com.soundrecorder.playback.newconvert.search.ConvertSearchFragment
import com.soundrecorder.playback.newconvert.ui.ConvertViewContainer
import com.soundrecorder.playback.view.MarkListBottomSheetDialogFragment
import com.soundrecorder.playback.view.MarkListContainerFragment
import com.soundrecorder.playback.view.SegmentSeekBar
import com.soundrecorder.player.TimerTickCallback
import com.soundrecorder.player.speaker.SpeakerModeController
import com.soundrecorder.player.speaker.SpeakerStateManager
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.wavemark.mark.MarkListAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Date

@Suppress("LargeClass", "LongMethod")
class PlaybackContainerFragment : Fragment(), View.OnClickListener, OnFileEventListener,
    IIPictureMarkListener<MarkMetaData, MarkDataBean>, OnBackPressedListener,
    IPlayBackContainerListener,
    OnFragmentUserChangeListener {

    companion object {
        /*
        延迟showLoading时间，默认0
        */
        const val ARG_KEY_SHOW_LOADING = "key_show_loading"

        const val MAX_PROGRESS = 1000
        const val THREE_SECONDS = 3000
        const val TIP_SHOW_DELAY_TIME = 300L
        const val SEARCH_INPUT_MAX_LENGTH = 50
        const val REQUEST_CODE_SYS_RENAME_AUTH = 211
        const val REQUEST_CODE_SYS_DELETE_AUTH = 212
        const val REQUEST_CODE_SYS_RECOVER_AUTH = 213
        const val MENU_POSITION_0 = 0

        private const val TAG = "PlaybackContainerFragment"

        private const val EKY_IS_IN_CONVERT_SEARCH = "key_is_in_convert_search"
        private const val KEY_IN_CONVERT_SEARCH_VALUE = "key_convert_search_value"
        private const val KEY_CONVERT_SEARCH_CURRENT_POS = "key_convert_search_current_pos"
        private const val KEY_CONVERT_SEARCH_LAST_POS = "key_convert_search_last_pos"
        private const val MARK_LIST_FRAGMENT_TAG = "MarkListView"
        private const val MARK_LIST_SHOW_STATE = "MARK_LIST_SHOW_STATE"
        private const val MARK_LIST_SHOW = "SHOW"
    }

    var mConvertManagerImpl: IConvertManager? = null
    var convertViewContainer: ConvertViewContainer? = null
    var mViewModel: PlaybackActivityViewModel? = null

    private var mBrowseFileActivityViewModel: ViewModel? = null
    private var speakerModeController: SpeakerModeController? = null
    private var mTabFragmentList: MutableList<Triple<String, String, Fragment>> = mutableListOf()
    private var mDeleteDialog: DeleteFileDialog? = null
    private var mDetailDialog: AlertDialog? = null
    private var mRenameDialog: RenameFileDialog? = null
    private var mSpeakerMenuItem: MenuItem? = null
    private var mDeleteMenuItem: MenuItem? = null
    private var mRecoverMenuItem: MenuItem? = null
    private var mPlaySettingMenuItem: MenuItem? = null
    private var mCutMenuItem: MenuItem? = null
    private var mSearchMenuItem: MenuItem? = null
    private var mConvertRoleMenuItem: MenuItem? = null
    private var mStartEditLauncher: ActivityResultLauncher<Intent>? = null
    private var mAudioFragment: PlaybackAudioFragment? = null
    private var mConvertFragment: PlaybackConvertFragment? = null
    private var mSummaryFragment: Fragment? = null
    private var mConvertSearchFragment: ConvertSearchFragment? = null
    private var mPagerAdapter: FragmentStateAdapter? = null
    private var summaryInterface: IAISummaryInterface? = null

    /*
    旧版分享txt，转文本文件超过50M的时候，显示“请稍后...”dialog
    链接分享,生成链接时，显示"正在生成…"dialog
    */
    private var mLoadingDialog: LoadingDialog? = null
    private var mPageChangeCallback: ViewPager2.OnPageChangeCallback? = null

    //播放设置弹窗
    private var bottomSheetDialogFragment: COUIBottomSheetDialogFragment? = null
    private var mPlaybackConvertViewModel: PlaybackConvertViewModel? = null
    private var mPictureMarkHelper: IPictureMarkDelegate<MarkMetaData>? = null
    private lateinit var binding: FragmentPlaybackContainerBinding
    private var mLoadingViewControl: LoadingViewControl? = null
    private var seekBar: SegmentSeekBar? = null
    private val markMenuList by lazy { ArrayList<PopupListItem>() }
    private val subMarkMenuList by lazy { ArrayList<PopupListItem>() }

    private var searchAnimView: COUISearchBar? = null
    private var disableDialog: AlertDialog? = null
    private var mRecoverDialog: RecoverFileDialog? = null
    private var immersiveAnimationHelperInitial: Boolean? = null

    //切换沉浸态动画
    private val immersiveAnimationHelper: ConvertImmersiveAnimationHelper by lazy {
        immersiveAnimationHelperInitial = true
        ConvertImmersiveAnimationHelper(requireContext(), mViewModel, binding)
    }

    //系统任务栏的高度
    private var navigationHeight: Int? = null
    private var windowType: WindowType? = null

    //分享弹窗关闭监听
    private val shareDialogDismissListener by lazy {
        OnDismissListener {
            DebugUtil.i(TAG, "shareDialogDismissListener")
            mViewModel?.mNeedShowShareDialog?.value = false
        }
    }

    private var mToolbarOverflowPopupWindow: COUIPopupListWindow? = null
    private var mSubMenuCheckedPosition = 0
    private var isClickAiTitle: Boolean = false

    private var mSmartNameMangerImpl: ISmartNameManager? = null
    private var mSupportSmartName: Boolean = false
    private var mFilePermissionDialog: AlertDialog? = null

    private var mTimerTickCallback: TimerTickCallback? = null
    private var currentPosition: Int = TAB_INDEX_FIRST
    private var mMarkListAdapter: MarkListAdapter? = null
    private var markListDialogFragment: MarkListBottomSheetDialogFragment? = null
    //触发切换沉浸态第一段动画可滚动的距离
    private var immersiveFirstMoveDownDistance = 0F

    //触发切换沉浸态列表滚动的距离
    private var switchImmersiveStateDistance: Int = 0

    //沉浸态第一段动画操作栏下移距离与列表实际滚动距离的比率
    private var moveDownDistanceRatio = 0F
    private var isInitImmersive = false

    private val shareListener = object : IShareListener {
        override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
            DebugUtil.i(TAG, "onShowShareWaitingDialog type:$type")
            activity?.runOnUiThread {
                showWaitingDialog(type)
            }
        }

        override fun onShareSuccess(mediaId: Long, type: ShareType) {
            DebugUtil.i(TAG, "onShareSuccess，type >> $type")
            activity?.runOnUiThread {
                dismissDialog()
            }
            unregisterShareListener()
        }

        override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
            DebugUtil.i(TAG, "onShareFailed，type >> $type  error:$error  message:$message")
            activity?.runOnUiThread {
                dismissDialog()
            }
            unregisterShareListener()
        }
    }

    private fun unregisterShareListener() {
        shareAction?.unregisterShareListener(shareListener)
    }

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val editRecordApi by lazy {
        Injector.injectFactory<EditRecordInterface>()
    }

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val cloudTipManagerAction by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    private val seedingApi by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    private val waveMarkApi by lazy {
        Injector.injectFactory<WaveMarkInterface>()
    }

    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }

    private val playbackAction by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    private val summaryAction by lazy {
        Injector.injectFactory<SummaryInterface>()
    }


    private val convertReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val mediaId = intent?.getLongExtra(KEY_NOTIFY_RECORD_ID, -1L) ?: -1L
            val isConvert = intent?.getBooleanExtra(KEY_NOTIFY_CONVERT_STATUS, false)
            DebugUtil.d(
                TAG, "ConvertReceiver, intent?.action:${intent?.action} , " +
                        "KEY_NOTIFY_CONVERT_STATUS:$isConvert ,KEY_NOTIFY_RECORD_ID:$mediaId "
            )
            when (intent?.action) {
                NOTIFY_CONVERT_STATUS_UPDATE -> {
                    if (isConvert == true && mediaId == mViewModel?.recordId) {
                        mPlaybackConvertViewModel?.updateConvertStatus(CONVERT_STATUS_PROGRESS)
                    }
                }

                NOTIFY_SMART_NAME_STATUS_UPDATE -> {
                    if (mediaId == mViewModel?.recordId) {
                        val resultName = intent.getStringExtra(KEY_NOTIFY_SMART_NAME_NAME_TEXT)
                        val display = intent.getBooleanExtra(KEY_NOTIFY_SMART_NAME_STATUS, false)
                        DebugUtil.d(TAG, "onReceive, display:$display, smartName:$resultName")
                        getConvertFragment()?.mConvertManagerImpl?.onSmartNameStatusChange(display, resultName)
                        summaryInterface?.onRecordNameStatusChange(display, resultName)
                        if (!resultName.isNullOrEmpty()) {
                            mViewModel?.renameRecordByCore2Full(resultName)
                        }
                    }
                }
            }
        }
    }

    private val summaryCallback: IAISummaryCallback = object : IAISummaryCallback {
        override fun onSummaryStart() {
            super.onSummaryStart()
            buttonPanelMaybeInVisible()
        }

        override fun onSummaryEnd() {
            super.onSummaryEnd()
            buttonPanelMaybeVisible()
        }
    }

    private val immersiveCallback: IImmersiveCallback = object : IImmersiveCallback {
        override fun updateIsImmersive(dx: Int, dy: Int) {
            <EMAIL>(dy)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBrowseFileActivityViewModel = browseFileApi?.getBrowseActivityViewModel(activity as? AppCompatActivity)
        if (ScreenUtil.isSmallScreen(context) && (browseFileApi?.getViewModelClickedToRecord(mBrowseFileActivityViewModel) == true)) {
            /*进入了录制页面，再小屏下，退出播放详情*/
            activity?.supportFragmentManager?.commit {
                remove(this@PlaybackContainerFragment)
            }
            browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
        }
        registerReceivers()
    }

    private fun initConvertManager() {
        val viewModel = mViewModel ?: return
        val convertViewContainer = this.convertViewContainer ?: return
        if (viewModel.recordId != -1L) {
            mConvertManagerImpl = ConvertManagerImpl()
            DebugUtil.i(TAG, "initConvertManager recordId:${viewModel.recordId} convertSupportType:${viewModel.convertSupportType}")
            getConvertFragment()?.mConvertManagerImpl = mConvertManagerImpl
            mConvertManagerImpl?.register(this, convertViewContainer, viewModel.recordId, viewModel.convertSupportType)
            mConvertManagerImpl?.setExportMenuItem()
            mConvertManagerImpl?.setViewModel(viewModel)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val rootView = inflater.inflate(R.layout.fragment_playback_container, container, false)
        binding = FragmentPlaybackContainerBinding.bind(rootView)
        activity?.let {
            val convertViewContainer = ConvertViewContainer(it)
            convertViewContainer.id = com.soundrecorder.common.R.id.convert_view_container
            this.convertViewContainer = convertViewContainer
        }
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        DebugUtil.d(TAG, "onViewCreated, savedInstanceState is null:${savedInstanceState == null}")
        initViewModel(savedInstanceState)
        configToolbar()
        initStartLauncher()
        initTab(savedInstanceState != null)
        initSeekBar()
        checkShowLoading()
        initAccess()
        initBrowseActivityViewModelObserver()
        initViewModelObserver()
        initOtherViews()
        initiateWindowInsets(binding.rootView)
        initListener()
        checkDialogRestore(savedInstanceState != null)
        checkDialogShowStatus(savedInstanceState != null)
        initSmartNameManagerImpl()
        initMarkMenu()
        initMarkList(savedInstanceState)
        initMarkListObserver()
        observerButtonPanelHeight()
        observe()
    }

    private fun initSmartNameManagerImpl() {
        if (mSmartNameMangerImpl == null) {
            mSmartNameMangerImpl = playbackAction?.getSmartNameManager()
        }
    }

    private fun initMarkMenu() {
        val mark = resources.getString(com.soundrecorder.common.R.string.talkback_flag)
        val picMark = resources.getString(com.soundrecorder.common.R.string.picture_mark)
        val takePhoto = resources.getString(com.soundrecorder.common.R.string.take_photo)
        val useAlbum = resources.getString(com.soundrecorder.common.R.string.use_album)

        subMarkMenuList.apply {
            clear()
            add(Builder().setTitle(takePhoto).build())
            add(Builder().setTitle(useAlbum).build())
        }
        markMenuList.apply {
            clear()
            add(Builder().setTitle(mark).build())
            add(Builder().setTitle(picMark).setSubMenuItemList(subMarkMenuList).build())
        }
    }

    private fun observerButtonPanelHeight() {
        binding.buttonPanel.clButtonPanel.observeHeightChange { height ->
            DebugUtil.d(TAG, "observerButtonPanelHeight height = $height")
            binding.flConvertAudioContainer.updatePadding(bottom = height)
            mConvertFragment?.updatePaddingBottom(height)
            (mSummaryFragment as? IAISummaryInterface)?.setPaddingBottom(height)
            navigationHeight?.let { (mSummaryFragment as? IAISummaryInterface)?.setBottomMargin(it) }
        }
    }

    private fun observe() {
        mViewModel?.showShareLinkPanel?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareLinkPanel $it ${mViewModel?.currentLifecycleState}")
            if (it?.isNotEmpty() == true && mViewModel?.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                activity?.let { activity ->
                    shareAction?.showShareLinkPanel(activity, it, null)
                    mViewModel?.showShareLinkPanel?.value = null
                }
            }
        }

        mViewModel?.showShareToast?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareToast $it ${mViewModel?.currentLifecycleState}")
            if (it != null && mViewModel?.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                ToastManager.showShortToast(context, it)
                mViewModel?.showShareToast?.value = null
            }
        }
    }

    private fun initMarkListObserver() {
        mViewModel?.lastMarkAction?.observe(viewLifecycleOwner) {
            DebugUtil.i(TAG, "lastMarkAction changed $it")
            when (it) {
                MARK_ACTION_INIT -> notifyMarkListByData { markListDialogFragment?.setDialogContentViewState() }
                MARK_ACTION_DELETE -> {
                    notifyMarkListByData { markListDialogFragment?.setDialogContentViewState() }
                    BuryingPoint.addMarkDelete(mViewModel?.recordType?.value ?: 0)
                }
                MARK_ACTION_RENAME -> {
                    notifyMarkListByData()
                    BuryingPoint.addMarkRename(mViewModel?.recordType?.value ?: 0)
                }
                MARK_ACTION_ADD -> {
                    kotlin.runCatching {
                        val index = mViewModel?.addMarkIndex
                        mMarkListAdapter?.setShowAnimatorPos(index ?: 0)
                        notifyMarkListByData { markListDialogFragment?.setDialogContentViewState() }
                        if (mViewModel?.playerController?.playerState?.value in arrayOf(
                                PlayStatus.PLAYER_STATE_PAUSE,
                                PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
                                PlayStatus.PLAYER_STATE_HALTON
                            )
                        ) {
                            BuryingPoint.addMarkWhenUnplay()
                        }
                        BuryingPoint.addMarkAdd(mViewModel?.recordType?.value ?: 0)
                    }.onSuccess {
                        DebugUtil.w(TAG, "Add mark success.")
                    }.onFailure {
                        DebugUtil.e(TAG, "Add mark error.")
                    }
                }
            }
        }

        mViewModel?.isShowMarkList?.observe(viewLifecycleOwner) { isShowing ->
            DebugUtil.i(TAG, "isShowing = $isShowing")
            if (isShowing) {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_SHOWING)
            } else {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_UNSHOWING)
            }
            changeMarkListViewDisplayState(isShowing)
        }
    }

    private fun notifyMarkListByData(commitCallback: Runnable? = null) {
        mViewModel?.getMarkList()?.value?.let {
            mMarkListAdapter?.setData(it, commitCallback)
        }
    }

    private fun initViewModel(savedInstanceState: Bundle?) {
        (activity as? AppCompatActivity)?.let {
            val isFromOtherProcess = browseFileApi?.getViewModelIsFromOther(mBrowseFileActivityViewModel) ?: false
            val supportConvertType = ConvertSupportManager.getConvertSupportType(!isFromOtherProcess)
            mPictureMarkHelper =
                waveMarkApi?.newPictureMarkDelegate(object : IPictureMarkLifeOwnerProvider {

                    override fun provideLifeCycleOwner(): LifecycleOwner =
                        this@PlaybackContainerFragment

                    override fun provideActivity(): AppCompatActivity = it

                    override fun isFinishing(): Boolean =
                        ((activity?.isFinishing == true) || (<EMAIL>))
                }, (savedInstanceState != null), this)
            mPlaybackConvertViewModel =
                ViewModelProvider(this)[PlaybackConvertViewModel::class.java].apply {
                    if ((savedInstanceState != null) && supportConvertType != ConvertSupportManager.CONVERT_DISABLE) {
                        if (!recordFilterIsRecycle()) {
                            savedInstanceState.getBoolean(EKY_IS_IN_CONVERT_SEARCH, false)
                                .let { isConvertSearch ->
                                    DebugUtil.d(TAG, "initViewModel, isConvertSearch:$isConvertSearch")
                                    mIsInConvertSearch.value = isConvertSearch
                                }
                        }
                        mConvertSearchValue =
                            savedInstanceState.getString(KEY_IN_CONVERT_SEARCH_VALUE, "")
                        currentPos = savedInstanceState.getInt(KEY_CONVERT_SEARCH_CURRENT_POS, 0)
                        lastPos = savedInstanceState.getInt(KEY_CONVERT_SEARCH_LAST_POS, 0)
                    }
                }
            mViewModel = ViewModelProvider(this)[PlaybackActivityViewModel::class.java].apply {
                isRecycle = browseFileApi?.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel)?.value?.isRecycle ?: false
                mIsFromOtherApp = isFromOtherProcess
                convertSupportType = supportConvertType
                isAddPictureMarkingCallback = {
                    <EMAIL>?.isAddPictureMarking()
                        ?: MutableLiveData(false)
                }
                oShareConvertTextPath = activity?.intent?.getStringExtra(OShareConvertUtil.EXTRA_OSHARE_CONVERT_TEXT_PATH)
                /*
                 * 注：<1>一定要放在 isAddPictureMarkingCallback初始化之后
                 *    <2>需要mPlaybackConvertViewModel?.mIsInConvertSearch先初始化
                 */
                observeNotificationBtn(
                    savedInstanceState != null,
                    mPlaybackConvertViewModel?.mIsInConvertSearch
                )
            }
            mViewModel?.initLifecycle(this)
        }
        TimeSetUtils(viewLifecycleOwner) {
            mViewModel?.playerController?.playerState?.value = mViewModel?.playerController?.playerState?.value
        }
    }

    private fun checkDialogShowStatus(isFromRestore: Boolean) {
        if (!isFromRestore) {
            DebugUtil.i(TAG, "checkDialogShowStatus not from restore, no need to check Dialog Show")
            return
        }
        if (mViewModel?.mNeedShowMarkRenameDialog?.value == true) {
            DebugUtil.i(TAG, "checkDialogShowStatus mMarkRenameDialogShow")
            mMarkListAdapter?.showRenameMark(
                activity,
                mViewModel?.mMarkRenameData,
                mViewModel?.mRenameMarkEditText
            )
        }
        if (mViewModel?.mNeedShowMarkDeleteDialog == true) {
            mMarkListAdapter?.showDeleteMark(activity, mViewModel?.mMarkDeleteData)
        }
    }

    private fun initListener() {
        binding.buttonPanel.imgMarkAdd.setOnClickListener(this)
        binding.buttonPanel.imgForward.setOnClickListener(this)
        binding.buttonPanel.imgBackward.setOnClickListener(this)
        binding.buttonPanel.imgMarkList.setOnClickListener(this)
        binding.buttonPanel.redCircleIcon.setOnClickListener(this)
        mViewModel?.setShareCallBack(shareListener)
    }

    private fun checkDialogRestore(isFromRestore: Boolean) {
        if (!isFromRestore) {
            DebugUtil.i(TAG, "checkDialogRestore not from restore, no need to check Dialog Show")
            return
        }
        if (mViewModel?.mNeedShowRenameDialog?.value == true) {
            DebugUtil.i(TAG, "checkDialogRestore showRenameDialog")
            showRenameDialog(mViewModel?.mRenameEditText)
        }
        if (mViewModel?.getNeedRestoreWaitingDialog() == true) {
            DebugUtil.i(TAG, "checkDialogRestore waitingDialog")
            //没有完成分享前置流程，则显示waitDialog
            showWaitingDialog(mViewModel?.getShareWaitingType())
        }
    }

    private fun findFragmentForPosition(position: Int): Fragment? {
        if (isAdded) {
            return childFragmentManager.findFragmentByTag("f" + mPagerAdapter?.getItemId(position))
        }
        return null
    }

    fun onPermissionGranted() {
        if (!PermissionUtils.hasReadAudioPermission()) {
            DebugUtil.i(TAG, "not hasReadAudioPermission")
            return
        }
        if (mViewModel?.recordId != null && mViewModel?.recordId != -1L) {
            mViewModel?.readMarkTag()
        }
    }

    fun getAudioFragment(): PlaybackAudioFragment? {
        //判断isAdded，防止出现异常：java.lang.IllegalStateException: Fragment has not been attached yet.
        if (isAdded) {
            mAudioFragment = childFragmentManager.findFragmentByTag(PlaybackAudioFragment.TAG) as? PlaybackAudioFragment
            if (mAudioFragment == null) {
                mAudioFragment = PlaybackAudioFragment()
            }
            return mAudioFragment
        }
        return null
    }

    fun getConvertFragment(): PlaybackConvertFragment? {
        if (mConvertFragment == null) {
            val fragment = findFragmentForPosition(TAB_INDEX_FIRST)
            if (fragment is PlaybackConvertFragment) {
                mConvertFragment = fragment
            }
        }
        return mConvertFragment
    }

    private fun getSummaryFragment(): Fragment? {
        if (mSummaryFragment == null) {
            mSummaryFragment = findFragmentForPosition(TAB_INDEX_SECOND)
        }
        return mSummaryFragment
    }

    private fun getConvertSearchFragment(): ConvertSearchFragment? {
        //判断isAdded，防止出现异常：java.lang.IllegalStateException: Fragment has not been attached yet.
        if (isAdded) {
            mConvertSearchFragment = childFragmentManager.findFragmentByTag(ConvertSearchFragment.TAG) as? ConvertSearchFragment
            if (mConvertSearchFragment == null) {
                mConvertSearchFragment = ConvertSearchFragment()
            }
            return mConvertSearchFragment
        }
        return null
    }

    private fun findConvertSearchFragment(): ConvertSearchFragment? {
        if (isAdded) {
            mConvertSearchFragment = childFragmentManager.findFragmentByTag(ConvertSearchFragment.TAG) as? ConvertSearchFragment
        }
        return mConvertSearchFragment
    }

    private fun initOtherViews() {
        adaptRtlView()
        binding.buttonPanel.imgMarkAdd.isVisible = !recordFilterIsRecycle()
        binding.buttonPanel.imgMarkList.isVisible = !recordFilterIsRecycle()
    }

    private fun adaptRtlView() {
        if (BaseApplication.sIsRTLanguage) {
            // RTl语言下，快进快退图标也需要适配RTL
            binding.buttonPanel.imgBackward.setImageResource(R.drawable.ic_forward)
            binding.buttonPanel.imgForward.setImageResource(R.drawable.ic_backward)
        }
    }

    private fun initAccess() {
        //play button support accessibility. the view should be red_circle_icon, not the parent view middle_control
        binding.buttonPanel.redCircleIcon.accessibilityDelegate = object : View.AccessibilityDelegate() {
            override fun sendAccessibilityEvent(host: View, eventType: Int) {
                if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                    if (mViewModel?.playerController?.isWholePlaying() == true) {
                        binding.buttonPanel.redCircleIcon.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_pause
                        )
                    } else {
                        binding.buttonPanel.redCircleIcon.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_play
                        )
                    }
                }
                super.sendAccessibilityEvent(host, eventType)
            }
        }
    }

    override fun onResume() {
        DebugUtil.i(TAG, "onResume")
        super.onResume()
        checkDeleteOrRenameDialogOnResume()
    }

    override fun onPause() {
        DebugUtil.i(TAG, "onPause")
        super.onPause()
        if (speakerModeController?.mIsSpeakerOn?.value == false) {
            mViewModel?.playerController?.pausePlay()
        }
        releasePopupWindow()
    }

    private fun checkDeleteOrRenameDialogOnResume() {
        mViewModel?.let {
            if (it.isNeedSmartName && it.needSmartNameMediaList.isNotEmpty()) {
                startConvertAndSmartName(it.needSmartNameMediaList)
            }
        }
        if (mRenameDialog?.getOperating() == true) {
            DebugUtil.i(TAG, "check resume  rename")
            if (PermissionUtils.hasFilePermissionCompat()) {
                val renameContent = mRenameDialog?.getRenameContent() ?: ""
                renameAgain(mViewModel?.playerController?.getPlayUri(), renameContent)
            }
            mRenameDialog?.resetOperating()
        }

        if (mDeleteDialog?.getOperating() == true) {
            DebugUtil.i(TAG, "check resume delete")
            if (PermissionUtils.hasFilePermissionCompat()) {
                val activity = activity ?: return
                val record = mViewModel?.getRecord() ?: return
                val isRecycle = mViewModel?.isRecycle ?: return
                val success = FileDealUtil.deleteRecord(activity, record, REQUEST_CODE_SYS_DELETE_AUTH, isRecycle)
                if (success) {
                    onDeleteRecordSuccess(isRecycle)
                    DeleteSoundEffectManager.getInstance().playDeleteSound()
                }
            }
            mDeleteDialog?.resetOperating()
        }

        if (mRecoverDialog?.getOperating() == true) {
            DebugUtil.i(TAG, "check resume ")
            if (PermissionUtils.hasFilePermissionCompat()) {
                val activity = activity ?: return
                val success = FileDealUtil.recoveryRecord(activity, getOperaRecord(), REQUEST_CODE_SYS_DELETE_AUTH)
                if (success) {
                    onRecoverRecordSuccess()
                }
            }
            mRecoverDialog?.resetOperating()
        }
    }

    private fun renameAgain(uri: Uri?, renameContent: String): Boolean {
        val suffix = mViewModel?.getRecordSuffix()
        if (FileDealUtil.renameAgain(uri, renameContent, suffix)) {
            mViewModel?.renameRecordByCore2Full(renameContent)
            notifyRefreshRecordList()
            return true
        }
        return false
    }

    private fun initStartLauncher() {
        //registerForActivityResult instead of  startActivityForResult
        mStartEditLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult? ->
            when (result?.resultCode) {
                Activity.RESULT_OK -> {

                    if (result.data?.getBooleanExtra(Constants.KEY_IS_CLIPPED_SAVE, false) == true) {
                        val newMediaId = result.data?.getLongExtra(Constants.KEY_CLIPPED_SAVE_RECORD_MEDIA_ID, -1) ?: -1
                        val isSmallWindow =
                            browseFileApi?.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.value == WindowType.SMALL
                        val playModel = if ((newMediaId > 0) && (!isSmallWindow)) {
                            /*裁切成功，若为父子级布局，则播放页面更新为裁切音频*/
                            StartPlayModel(newMediaId).apply {
                                isFromOtherApp = mViewModel?.mIsFromOtherApp ?: false
                            }
                        } else {
                            null
                        }
                        // 中大屏裁切保存新文件，右侧播放更新为新文件详情,小屏下保存成功回到列表页面
                        browseFileApi?.setViewModelPlayData(mBrowseFileActivityViewModel, playModel)
                        notifyCutNewRecordChange()
                        if (isFromAppCard()) {
                            startToBrowseFile()
                        } else {
                            if (isSmallWindow) {
                                DebugUtil.d(TAG, "remove play fragment")
                                // 小屏从裁切回来,直接移除播放fragment，避免移除动效不执行，fragment没被移除
                                activity?.supportFragmentManager?.commit { remove(this@PlaybackContainerFragment) }
                            }
                        }
                    }
                }

                Constants.RESULT_CODE_FILEOBSERVER_FINISH -> browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
            }
        }
    }

    private fun notifyCutNewRecordChange() {
        val intent = Intent(RecordFileChangeNotify.FILE_CUT_NEW_RECORD_ACTION)
        intent.putExtra(Constants.FRESH_FLAG, true)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    private fun startToBrowseFile() {
        activity?.let {
            browseFileApi?.createBrowseFileIntent(it)?.run {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(this)
            }
            it.finish()
        }
    }

    private fun initTab(isFromRestore: Boolean) {
        mPagerAdapter = object : FragmentStateAdapter(this@PlaybackContainerFragment) {
            override fun createFragment(position: Int): Fragment {
                val triple = mTabFragmentList[position.coerceAtMost(mTabFragmentList.size - 1)]
                return triple.third
            }

            override fun getItemCount(): Int {
                return mTabFragmentList.size
            }
        }

        mPageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                DebugUtil.i(TAG, "onPageSelected position $position")
                super.onPageSelected(position)
                handlePageSelected(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                binding.tabLayout.onPageScrollStateChanged(state)
                if (state == 0) {
                    fragmentScrollEnd()
                }
            }

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                binding.tabLayout.onPageScrolled(position, positionOffset, positionOffsetPixels)
                fragmentScroll(position, positionOffset)
            }
        }
        binding.tabLayout.setOnSelectedSegmentChangeListener { _, position, _ ->
            handleTabLayoutSegmentChange(position)
        }
        binding.tabLayout.setResponsiveWidthEnabled(false)
        binding.viewpager.apply {
            getChildAt(0).isNestedScrollingEnabled = false
            offscreenPageLimit = 1
            adapter = mPagerAdapter
            registerOnPageChangeCallback(mPageChangeCallback as ViewPager2.OnPageChangeCallback)
        }
        //如果是搜索过来的，就需要更新type，需要在addfragment后刷新最新的位置
        setViewPagePosition(isFromRestore)
    }

    private fun setViewPagePosition(isFromRestore: Boolean) {
        DebugUtil.i(TAG, "setViewPagePosition isFromRestore:$isFromRestore")
        if (isFromRestore.not()) {
            val playModel = browseFileApi?.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel)?.value
            val searchTapType = if (playModel?.selectPosInPlayback == TAB_INDEX_SECOND) TAB_TYPE_SUMMARY else TAB_TYPE_CONVERT
            DebugUtil.i(TAG, "searchTapType = $searchTapType")
            mViewModel?.mCurrentTabType?.value = searchTapType
        }
    }

    private fun handleTabLayoutSegmentChange(position: Int) {
        if (position < 0) {
            DebugUtil.d(TAG, "handleTabLayoutSegmentChange position error")
            return
        }
        val currentTabType = mTabFragmentList[position.coerceAtMost(mTabFragmentList.size - 1)].first
        if (currentTabType != mViewModel?.mCurrentTabType?.value) {
            mViewModel?.mCurrentTabType?.value = currentTabType
            binding.viewpager.setCurrentItem(position, binding.body.isVisible)
        }
    }

    private fun handlePageSelected(position: Int) {
        if (position < 0) {
            DebugUtil.d(TAG, "handlePageSelected position error")
            return
        }
        val currentTabType = mTabFragmentList[position.coerceAtMost(mTabFragmentList.size - 1)].first
        this.currentPosition = position
        if (currentTabType == TAB_TYPE_SUMMARY) {
            handleSelectSummaryPage(position)
        } else {
            handleSelectConvertPage(position, currentTabType)
        }
    }

    private fun handleSelectSummaryPage(position: Int) {
        (mSummaryFragment as? IAISummaryInterface)?.apply {
            this.checkSummaryAvailable { available ->
                DebugUtil.i(TAG, "checkSummaryAvailable available = $available")
                val runnable = {
                    if (available) {
                        binding.tabLayout.onPageSelected(position)
                        fragmentSelected()
                        BuryingPoint.addPlaySwitchTab(position)
                    } else {
                        binding.viewpager.currentItem = TAB_INDEX_FIRST
                        binding.tabLayout.onPageSelected(TAB_INDEX_FIRST)
                    }
                }
                runOnMain(runnable)
            }
        }
    }

    private fun handleSelectConvertPage(position: Int, currentTabType: String) {
        binding.tabLayout.onPageSelected(position)
        if (currentTabType == TAB_TYPE_CONVERT) {
            checkNeedStartConvertStartAnimation()
        }
        fragmentSelected()
        BuryingPoint.addPlaySwitchTab(position)
    }

    private fun clickTransferTextSelectTab() {
        if (!recordFilterIsRecycle()) {
            binding.tabLayout.onPageSelected(
                tabTypeToPosition(TAB_TYPE_CONVERT)
            )
        }
    }

    private fun tabTypeToPosition(tabType: String): Int {
        mTabFragmentList.forEachIndexed { index, s ->
            if (s.first == tabType) {
                return index
            }
        }
        return 0
    }

    /**
     * 每次进入转文本页面，仅需要播放一次初始动效
     */
    private fun checkNeedStartConvertStartAnimation() {
        getConvertFragment()?.let {
            if ((mViewModel?.isFirstInConvertFragment == true) &&
                (it.mConvertManagerImpl?.getConvertStatus()?.value == CONVERT_STATUS_INIT)
            ) {
                //本次进入没有执行过初始动画，则执行初始动画
                it.mConvertManagerImpl?.getConvertViewController()?.startConvertInitAnimation()
            }
            mViewModel?.isFirstInConvertFragment = false
        }
    }


    private fun initSeekBar() {
        layoutInflater.inflate(R.layout.layout_playback_seekbar, binding.buttonPanel.llSeekbarContainer, true)
        seekBar = binding.buttonPanel.llSeekbarContainer.findViewById(R.id.seek_bar)
        val seekBar = seekBar ?: return
        seekBar.max = MAX_PROGRESS
        seekBar.isHapticFeedbackEnabled = false
        seekBar.setOnSeekBarChangeListener(object : COUISeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: COUISeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    mViewModel?.let {
                        val seekToTime = progress * it.playerController.getDuration() / MAX_PROGRESS
                        it.needSyncRulerView = true
                        it.playerController.setOCurrentTimeMillis(seekToTime)
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: COUISeekBar?) {
                mViewModel?.let {
                    it.playerController.onStartTouchSeekBar()
                    if (it.playerController.isWholePlaying()) {
                        it.playerController.stopTimerNow()
                        getAudioFragment()?.waveStopMove()
                    }
                }
            }

            override fun onStopTrackingTouch(seekBar1: COUISeekBar?) {
                mViewModel?.let {
                    it.playerController.onStopTouchSeekBar()
                    seekBar.run {
                        val seekToTime = progress * it.playerController.getDuration() / MAX_PROGRESS
                        it.seekTime(seekToTime)
                    }
                    //按下之前是播放状态，松手后也应该继续播放。
                    it.playerController.onResetPlayState()
                }
            }
        })
    }

    private fun initBrowseActivityViewModelObserver() {
        browseFileApi?.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            if (it == null) {
                DebugUtil.i(TAG, "play data to be null")
                mViewModel?.cancelLoadData()
                return@observe
            }
            if (it.mediaId == mViewModel?.recordId) {
                DebugUtil.i(TAG, "play data id not change,id=${it.mediaId}")
                initConvertManager()
                return@observe
            }
            DebugUtil.i(TAG, "play record data changed to $it")
            /*音频发生变化*/
            mViewModel?.startPlayModel = it
            mViewModel?.autoPlay = it.autoPlay
            mPlaybackConvertViewModel?.isCheckSpeakerRole = it.isCheckSpeakerRole
            initConvertManager()
            onPermissionGranted()
        }
        browseFileApi?.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe current windowType=$it")
            windowType = it
            if (FeatureOption.getIsFoldFeature()) {
                mViewModel?.mShowActivityControlView?.value = false
            } else {
                mViewModel?.mShowActivityControlView?.value = (it == WindowType.LARGE)
            }
            /*小屏且未处于转文本搜索页面才显示返回箭头*/
            setNavigationIcon()
            binding.buttonPanel.redCircleIcon.windowType = it
            mPlayStateChangeObserver.onChanged(mViewModel?.playerController?.playerState?.value ?: PlayStatus.PLAYER_STATE_INIT)
            correctBottomButtonWhenLayoutNotReCreate(it == WindowType.LARGE)
            correctBottomViewMarginBottom()
            /*windowType发生变化，更新下*/
            if (mViewModel?.isImmersiveState?.value == true) {
                immersiveAnimationHelper.checkUpdateWindowTypeChanged(navigationHeight ?: 0, it)
            }
        }

        mViewModel?.isImmersiveState?.observe(viewLifecycleOwner) {
            //界面重建时需要等节布局绘制完成在切换到沉浸态
            if (binding.buttonPanel.root.width == 0) {
                binding.buttonPanel.root.post {
                    switchImmersive(it)
                }
            } else {
                switchImmersive(it)
            }
        }

        mViewModel?.immersiveMoveDownDistance?.observe(viewLifecycleOwner) {
            val params = binding.buttonPanel.root.layoutParams as MarginLayoutParams
            params.bottomMargin = -it
            binding.buttonPanel.root.layoutParams = params
        }
    }

    /**
     * 矫正layout改变，应用未重建，导致加载layout同实际layout不匹配，底部操作按钮显示异常问题
     * 平板上 录音同其他应用分屏加载小中布局，其他应用切位浮窗后，录音退出分屏显示全屏，此时录音未重建，仍然加载的小中布局
     */
    private fun correctBottomButtonWhenLayoutNotReCreate(isLarge: Boolean) {
        /*回收站或折叠屏不显示横向摘要、转文本等操作按钮布局，设为0F*/
        if (recordFilterIsRecycle() || FeatureOption.getIsFoldFeature()) {
            binding.buttonPanel.toolCenterGuideLine.setGuidelinePercent(0F)
            binding.buttonPanel.toolRightGuideLine.setGuidelinePercent(NumberConstant.NUM_F1_0)
        } else {
            val guidePercent = (binding.buttonPanel.toolCenterGuideLine.layoutParams as ConstraintLayout.LayoutParams).guidePercent
            if (isLarge && guidePercent != NumberConstant.NUM_F0_2) {
                binding.buttonPanel.toolCenterGuideLine.setGuidelinePercent(NumberConstant.NUM_F0_2)
                binding.buttonPanel.toolRightGuideLine.setGuidelinePercent(NumberConstant.NUM_F0_8)
            } else if (!isLarge && guidePercent != 0F) {
                binding.buttonPanel.toolCenterGuideLine.setGuidelinePercent(0F)
                binding.buttonPanel.toolRightGuideLine.setGuidelinePercent(NumberConstant.NUM_F1_0)
            }
        }
    }

    private fun correctBottomViewMarginBottom() {
        binding.buttonPanel.middleControl.run {
            updateLayoutParams<MarginLayoutParams> {
                bottomMargin =
                    resources.getDimension(com.soundrecorder.common.R.dimen.circle_playback_button_margin_bottom).toInt()
                val newWidth = resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam).toInt()
                if (width != newWidth) {
                    width = newWidth
                    height = newWidth
                    binding.buttonPanel.redCircleIcon.refreshCircleRadius(
                        resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_radius)
                    )
                }
            }
        }
    }

    private fun setNavigationIcon() {
        val isConvertSearch = mPlaybackConvertViewModel?.mIsInConvertSearch?.value
        DebugUtil.d(TAG, "setNavigationIcon, isInConvertSearch:$isConvertSearch")
        if (isFromAppCard() || (browseFileApi?.isSmallWindow(mBrowseFileActivityViewModel) == true && (isConvertSearch != true))) {
            binding.toolbar.setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
        } else {
            binding.toolbar.navigationIcon = null
        }
    }

    private fun initViewModelObserver() {
        mTimerTickCallback = object : TimerTickCallback {
            override fun onTimerTick(timeTickMillis: Long) {
                //DebugUtil.d(TAG, "refreshTimerTick: newTime = $timeTickMillis, needSyncRulerView = true")
                mViewModel?.onTimerRefreshTime(timeTickMillis)
            }
        }
        mViewModel?.playerController?.addTimerTickListener(mTimerTickCallback)

        mViewModel?.markEnable?.observe(viewLifecycleOwner) {
            refreshMarkEnable(it)
        }
        mViewModel?.playerController?.currentTimeMillis?.observe(viewLifecycleOwner) { currentTime ->
            refreshCurPlayTime(currentTime)
            if (mViewModel?.playerController?.mIsTouchSeekbar?.value == false) {
                refreshSeekBar(currentTime)
                /*若有target时间片段，未在时间片内，矫正播放时间片内数据*/
                mViewModel?.correctPlayTime(currentTime, mViewModel?.targetPlaySegment?.value) { seekTo, findCurOrNextNull ->
                    if (findCurOrNextNull && mViewModel?.playerController?.isWholePlaying() == true) {
                        mViewModel?.playerController?.doPausePlay()
                    }
                    mViewModel?.playerController?.seekTime(seekTo)
                }
            }
        }
        mViewModel?.playerController?.mDuration?.observe(viewLifecycleOwner) { duration ->
            binding.buttonPanel.tvDuration.contentDescription =
                TimeUtils.getContentDescriptionForTimeDuration(duration)
            binding.buttonPanel.tvDuration.text = duration.durationHasHourFormatTimeExclusive(true)
            seekBar?.setDuration(duration)
        }
        //observe the playState forever, remember to release it
        mViewModel?.playerController?.playerState?.observeForever(mPlayStateChangeObserver)
        mViewModel?.playerController?.playSpeedIndex?.observe(viewLifecycleOwner) {
            mViewModel?.playerController?.changePlayerSpeed()
        }

        val loadAmpObserver = Observer<Boolean> {
            if (it) {
                seekBar?.isEnabled = true // 拿到波形数据，恢复按钮点击状态
                if (browseFileApi?.getViewModelAnimRunning(mBrowseFileActivityViewModel)?.value != true) {
                    // 小屏动效执行完
                    PermissionUtils.checkNotificationPermission(activity)
                    handleViewHierarchy(false)
                }
            }
        }
        mViewModel?.mIsDecodeReady?.observe(viewLifecycleOwner, loadAmpObserver)
        mViewModel?.isPrepareAmplitudeAndMark?.observe(viewLifecycleOwner, loadAmpObserver)
        /*animEnd默认为null，作用等同于true的*/
        browseFileApi?.getViewModelAnimRunning(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            if (!it && (mViewModel?.loadAmpSuccess() == true)) {
                PermissionUtils.checkNotificationPermission(activity)
                handleViewHierarchy(false)
            }
        }

        browseFileApi?.getDeleteSummaryNoteIdLiveData(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            mViewModel?.handleClearSummaryCallId(it)
        }

        mPlaybackConvertViewModel?.isSpeakerRoleShowing?.observe(viewLifecycleOwner) { needShowRole ->
            mViewModel?.mPanelShowStatus?.value?.let { showStatus ->
                handlePanelShowStatus(showStatus)
            } ?: kotlin.run {
                DebugUtil.d(TAG, "isSpeakerRoleShowing mPanelShowStatus is null")
            }

            if (needShowRole) {
                mConvertRoleMenuItem?.title = getString(com.soundrecorder.common.R.string.export_hide_speaker)
            } else {
                mConvertRoleMenuItem?.title = getString(com.soundrecorder.common.R.string.export_show_speaker)
            }
            if (needShowRole) {
                getConvertFragment()?.stopScroll()
            } else {
                getConvertFragment()?.mConvertManagerImpl?.removeSpeakerTipTask()
                /*关闭讲话人，重置目标静音片、筛选讲话人数据*/
                mPlaybackConvertViewModel?.selectSpeakerList = null
                mViewModel?.targetPlaySegment?.value = null
            }
        }

        mViewModel?.mPanelShowStatus?.observe(viewLifecycleOwner) {
            handlePanelShowStatus(it)
        }

        mPlaybackConvertViewModel?.mConvertStatus?.observe(viewLifecycleOwner) {
            DebugUtil.i(TAG, "convertViewModelChange mConvertStatus changed: $it")
            handleViewHierarchy(mViewModel?.loadAmpSuccess() != true)
        }

        mViewModel?.mMimeType?.observe(viewLifecycleOwner) {
            mCutMenuItem?.isVisible = isSupportAudioCut(it)
        }
        mPlaybackConvertViewModel?.mIsInConvertSearch?.observe(viewLifecycleOwner) {
            setNavigationIcon()
            inAndOutConvertSearchFragment(it)
            browseFileApi?.onConvertSearchStateChanged(activity, it, !mPlaybackConvertViewModel?.mConvertSearchValue.isNullOrBlank()) {
                findConvertSearchFragment()?.onClickCancel()
            }
        }
        mViewModel?.targetPlaySegment?.observe(viewLifecycleOwner) { timeSegmentList ->
            /*
            更新seekbar 时间片高亮状态
            */
            seekBar?.setSegmentTimeMillList(timeSegmentList)
            if (timeSegmentList?.isNotEmpty() == true) {
                /*
                设置目标静音片后,若当前暂停状态，需要矫正当前时间在时间片内
                */
                mViewModel?.run {
                    resetCurrentPlaySegment()
                    if (playerController.hasPaused()) {
                        correctPlayTime(playerController.currentTimeMillis.getValueWithDefault(), timeSegmentList) { seekTo, _ ->
                            DebugUtil.i(TAG, " target changed, seek to $seekTo")
                            playerController.seekTime(seekTo)
                        }
                    }
                }
            }
        }
        initSpeakerMenuObservers()
        initDialog()
    }

    private fun handlePanelShowStatus(showStatus: PlaybackActivityViewModel.PanelShowStatus) {
        if (recordFilterIsRecycle()) {
            /*隱藏倍速等*/
            mPlaySettingMenuItem?.isVisible = false
            mCutMenuItem?.isVisible = false
        } else {
            setVisibleMenu(showStatus.mHasConvertContent, showStatus.mConvertShowSwitch)
        }
    }

    private fun setVisibleMenu(hasConvertContent: Boolean, roleVisible: Boolean) {
        val convertAndHasConvertContent = isOnConvert() && hasConvertContent
        // 讲话人
        if ((convertAndHasConvertContent && roleVisible) != mConvertRoleMenuItem?.isVisible) {
            mConvertRoleMenuItem?.isVisible = (convertAndHasConvertContent && roleVisible)
        }
        // 内容搜索
        if (convertAndHasConvertContent != mSearchMenuItem?.isVisible) {
            mSearchMenuItem?.isVisible = convertAndHasConvertContent
        }
    }

    private fun addTabFragment(tabType: String) {
        val triple = when (tabType) {
            TAB_TYPE_CONVERT -> {
                mConvertFragment = getConvertFragment() ?: PlaybackConvertFragment()
                mConvertFragment?.let {
                    val buttonPanelHeight = binding.buttonPanel.clButtonPanel.height
                    DebugUtil.d(TAG, "addTabFragment TAB_TYPE_CONVERT buttonPanelHeight = $buttonPanelHeight")
                    it.updatePaddingBottom(buttonPanelHeight)
                    Triple(TAB_TYPE_CONVERT, getString(com.soundrecorder.common.R.string.original_text), it)
                }
            }

            TAB_TYPE_SUMMARY -> {
                mSummaryFragment = getSummaryFragment() ?: newSummaryFragment()
                mSummaryFragment?.let { fragment ->
                    fragment.arguments = Bundle().apply {
                        putLong(BUNDLE_MEDIA_ID, mViewModel?.recordId ?: 0)
                        putInt(BUNDLE_RECORD_TYPE, mViewModel?.getRecord()?.recordType ?: -1)
                        putString(BUNDLE_RECORD_TITLE, mViewModel?.getRecord()?.displayName ?: "")
                        putLong(BUNDLE_RECORD_MODIFY_TIME, mViewModel?.getRecord()?.dateCreated ?: -1L)
                        putString(BUNDLE_RECORD_FILE_PATH, mViewModel?.getRecord()?.data ?: "")
                        putLong(BUNDLE_RECORD_FILE_DURATION, mViewModel?.getRecord()?.duration ?: 0)
                    }
                    val buttonPanelHeight = binding.buttonPanel.clButtonPanel.height
                    DebugUtil.d(TAG, "addTabFragment TAB_TYPE_SUMMARY buttonPanelHeight = $buttonPanelHeight")
                    (mSummaryFragment as? IAISummaryInterface)?.setPaddingBottom(buttonPanelHeight)
                    navigationHeight?.let { (mSummaryFragment as? IAISummaryInterface)?.setBottomMargin(it) }
                    (mSummaryFragment as? IAISummaryInterface)?.setSummaryCallback(summaryCallback)
                    (mSummaryFragment as? IAISummaryInterface)?.setImmersiveCallback(immersiveCallback)
                    Triple(TAB_TYPE_SUMMARY, getString(com.soundrecorder.common.R.string.summary), fragment)
                }
            }
            else -> null
        }
        triple?.let {
            mTabFragmentList.find { it.first == triple.first } ?: run {
                mTabFragmentList.add(triple)
                val newChild = TextView(context)
                newChild.tag = triple.first
                newChild.text = triple.second
                binding.tabLayout.addView(newChild)
                mPagerAdapter?.notifyItemInserted(mTabFragmentList.size - 1)
            }
        }
    }

    private fun newSummaryFragment(): Fragment? {
        if (summaryInterface == null) {
            summaryInterface = summaryAction?.getAISummaryInterface()
        }
        return summaryInterface?.getFragment()
    }

    /**
     * 是否支持裁切
     * @param mimeType 音频文件mimeType
     * @return boolean true: 支持 false：不支持
     */
    private fun isSupportAudioCut(mimeType: String?): Boolean {
        var supportMime = arrayOf(
            RecordConstant.MIMETYPE_MP3,
            RecordConstant.MIMETYPE_AMR,
            RecordConstant.MIMETYPE_AMR_WB,
            RecordConstant.MIMETYPE_3GPP
        )
        // 品牌融合后，支持aac和wav
        if (FunctionOption.IS_SUPPORT_WAV_AND_AAC) {
            supportMime = supportMime
                .plus(RecordConstant.MIMETYPE_ACC)
                .plus(RecordConstant.MIMETYPE_ACC_ADTS)
                .plus(RecordConstant.MIMETYPE_WAV)
        }
        return (mimeType in supportMime)
    }

    private fun refreshCurPlayTime(seekToTime: Long) {
        binding.buttonPanel.tvCurrent.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
        binding.buttonPanel.tvCurrent.text = seekToTime.currentHasHourFormatTimeExclusive(mViewModel?.playerController?.getDuration())
    }

    private fun refreshSeekBar(currentTime: Long) {
        val duration = mViewModel?.playerController?.getDuration() ?: 0
        if (duration > 0) {
            val seekBar = seekBar ?: return
            seekBar.progress = (currentTime * MAX_PROGRESS / (duration)).toInt()
            seekBar.contentDescription = RecorderTextUtils.getNewProgressDescription(
                BaseApplication.getAppContext(), currentTime, duration
            )
            seekBar.accessibilityDelegate = object : View.AccessibilityDelegate() {
                override fun sendAccessibilityEvent(host: View, eventType: Int) {
                    if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                        seekBar.contentDescription = RecorderTextUtils.getNewProgressDescription(
                            BaseApplication.getAppContext(), currentTime, duration
                        )
                    }
                    super.sendAccessibilityEvent(host, eventType)
                }
            }
        }
    }

    private fun refreshMarkEnable(isMarkEnable: Boolean) {
        binding.buttonPanel.imgMarkAdd.isEnabled = isMarkEnable && (mPictureMarkHelper?.checkNeedAddMark() == true)
    }

    private fun initMarkList(savedInstanceState: Bundle?) {
        mMarkListAdapter = MarkListAdapter(activity, true).apply {
            setOnDeleteListener {
                val index = mViewModel?.getMarkList()?.value?.indexOf(it) ?: return@setOnDeleteListener
                mViewModel?.removeMark(index)
                if (getAudioFragment()?.isResumed == true) {
                    getAudioFragment()?.mBinding?.waveRecyclerview?.removeMarkData = it
                }
            }
            setOnMarkClickListener { data ->
                if (getAudioFragment()?.isResumed == true) {
                    getAudioFragment()?.mBinding?.waveRecyclerview?.stopScroll()
                }
                markListDialogFragment?.setBottomSheetDialogFragmentState(COUIBottomSheetBehavior.STATE_COLLAPSED)
                mViewModel?.needSyncRulerView = true
                mViewModel?.seekTime(data.correctTime)
                BuryingPoint.seekToMarkTagWhenPlayback(mViewModel?.recordType?.value)
            }
            setOnRenameMarkListener(mViewModel?.onRenameMarkListener)
        }
        val systemCacheMarkListFragment = childFragmentManager.findFragmentByTag(MARK_LIST_FRAGMENT_TAG)
        if (savedInstanceState != null && systemCacheMarkListFragment != null) {
            val showStatus = savedInstanceState.getString(MARK_LIST_SHOW_STATE, "")
            DebugUtil.w(TAG, "initMarkList $showStatus")
            if (systemCacheMarkListFragment is COUIBottomSheetDialogFragment && showStatus == MARK_LIST_SHOW) {
                systemCacheMarkListFragment.dismiss()
            }
        }
    }

    private fun initDialog() {
        mViewModel?.mNeedShowRecoverDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showRecoverDialog()
            }
        }
        mViewModel?.mNeedShowRecycleDeleteDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showDeleteDialog(cloudTipManagerAction?.isCloudSwitchOn() == true, true)
            }
        }
        mViewModel?.mNeedShowDeleteDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showDeleteDialog(cloudTipManagerAction?.isCloudSwitchOn() == true, false)
            }
        }
        mViewModel?.mNeedShowDetailDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showDetailDialog()
            }
        }
        mViewModel?.mNeedShowSpeedDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showPlaySettingDialog()
            }
        }
        mViewModel?.mNeedShowShareDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                processExport(null)
            }
        }
    }

    private fun checkShowTabLayout(): Boolean {
        DebugUtil.d(TAG, "checkShowTabLayout convertStatus:${mPlaybackConvertViewModel?.mConvertStatus?.value}," +
                    "size:${mTabFragmentList.size}, " +
                    "convertSearch:${mPlaybackConvertViewModel?.mIsInConvertSearch?.value}")
        val result = (mPlaybackConvertViewModel?.mConvertStatus?.value == CONVERT_STATUS_COMPLETE
                || mPlaybackConvertViewModel?.mConvertStatus?.value == CONVERT_STATUS_SUMMARY_NONE_COMPLETE)
                && !recordFilterIsRecycle()
                && (mTabFragmentList.size > 1)
                && (mPlaybackConvertViewModel?.mIsInConvertSearch?.value != true)
        if (result) {
            binding.tabLayout.updateLayoutParams<ViewGroup.LayoutParams> {
                height = resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp36)
            }
        } else {
            binding.tabLayout.updateLayoutParams<ViewGroup.LayoutParams> {
                height = 0
            }
        }
        return result
    }

    private fun handleViewHierarchy(loadingShow: Boolean) {
        DebugUtil.d(TAG, "handleViewHierarchy loadingShow:$loadingShow")
        if (loadingShow) {
            mLoadingViewControl?.handleLoading(true)
            binding.buttonPanel.clButtonPanel.isVisible = false
            binding.flConvertAudioContainer.isVisible = false
            binding.viewpager.isVisible = false
            binding.tabLayout.isVisible = false
            binding.buttonPanel.tvCurrent.isVisible = false
            binding.buttonPanel.tvDuration.isVisible = false
        } else {
            val convertStatus = mPlaybackConvertViewModel?.mConvertStatus?.value
            DebugUtil.e(TAG, "handleViewHierarchy convertStatus:$convertStatus")
            when (convertStatus) {
                // 转写中
                CONVERT_STATUS_PROGRESS -> {
                    binding.flConvertAudioContainer.isVisible = false
                    binding.viewpager.isVisible = true
                    binding.viewpager.isUserInputEnabled = false
                    handleTabLayout()
                    seekBar?.isVisible = true
                    binding.buttonPanel.tvCurrent.isVisible = true
                    binding.buttonPanel.tvDuration.isVisible = true
                    binding.buttonPanel.clButtonPanel.isVisible = true
                }
                //转写完成
                CONVERT_STATUS_COMPLETE,
                //有便签摘要，但是没有转文本
                CONVERT_STATUS_SUMMARY_NONE_COMPLETE -> {
                    binding.flConvertAudioContainer.isVisible = false
                    binding.viewpager.isVisible = true
                    binding.viewpager.isUserInputEnabled = true
                    handleTabLayout()
                    seekBar?.isVisible = true
                    binding.buttonPanel.tvCurrent.isVisible = true
                    binding.buttonPanel.tvDuration.isVisible = true
                    binding.buttonPanel.clButtonPanel.isVisible = true
                    mViewModel?.mPanelShowStatus?.value?.let { showStatus ->
                        handlePanelShowStatus(showStatus)
                    } ?: run {
                        DebugUtil.d(TAG, "mPanelShowStatus is null")
                    }
                }
                else -> {
                    /*
                    CONVERT_STATUS_INIT, CONVERT_STATUS_CANCEL, CONVERT_STATUS_USERTIMEOUT , null值都要如此处理
                     */
                    binding.flConvertAudioContainer.isVisible = true
                    binding.viewpager.isVisible = false
                    binding.viewpager.isUserInputEnabled = true
                    binding.tabLayout.isVisible = false
                    seekBar?.isVisible = true
                    binding.buttonPanel.tvCurrent.isVisible = true
                    binding.buttonPanel.tvDuration.isVisible = true
                    binding.buttonPanel.clButtonPanel.isVisible = true
                }
            }
            inAndOutAudioFragment()
            showCurrentItem()
            mLoadingViewControl?.handleLoading(false)
        }
    }

    private fun showCurrentItem() {
        val position = tabTypeToPosition(mViewModel?.mCurrentTabType?.value ?: TAB_TYPE_CONVERT)
        binding.viewpager.post {
            if (isAdded.not()) return@post
            DebugUtil.i(TAG, "showCurrentItem position = $position")
            binding.viewpager.setCurrentItem(position, false)
        }
    }

    private fun handleTabLayout() {
        val convertShow = mPlaybackConvertViewModel?.mConvertStatus?.value == CONVERT_STATUS_COMPLETE
                || mPlaybackConvertViewModel?.mConvertStatus?.value == CONVERT_STATUS_PROGRESS
                || mPlaybackConvertViewModel?.mConvertStatus?.value == CONVERT_STATUS_SUMMARY_NONE_COMPLETE
        if (convertShow) {
            addTabFragment(TAB_TYPE_CONVERT)
        }
        val hasSummary = (mPlaybackConvertViewModel?.mConvertStatus?.value == CONVERT_STATUS_COMPLETE
                || mPlaybackConvertViewModel?.mConvertStatus?.value == CONVERT_STATUS_SUMMARY_NONE_COMPLETE)
                && mViewModel?.mPanelShowStatus?.value?.checkHasSummary() == true
        DebugUtil.d(TAG, "handleTabLayout convertShow = $convertShow, hasSummary = $hasSummary")
        if (hasSummary) {
            addTabFragment(TAB_TYPE_SUMMARY)
        }
        binding.tabLayout.isVisible = checkShowTabLayout()
    }

    /**
     * 检查并设置加载状态和Tab显示
     * 注意：此方法不再处理seekBar的启用状态，该逻辑已移至updateSeekBarState方法
     */
    private fun checkShowLoading() {
        if (mLoadingViewControl == null) {
            mLoadingViewControl = LoadingViewControl(lifecycle, binding.colorLoadView.colorLoadView)
        }
        val needShowLoading = arguments?.getBoolean(ARG_KEY_SHOW_LOADING, false) ?: false
        handleViewHierarchy(true)
        if (!needShowLoading) {
            // 默认不showLoading场景下，禁止拖动seekbar
            seekBar?.isEnabled = mViewModel?.loadAmpSuccess() ?: false
        }
    }

    private fun initSpeakerMenuObservers() {
        speakerModeController = browseFileApi?.getViewModelSpeakerModeController<SpeakerModeController>(mBrowseFileActivityViewModel)
        speakerModeController?.mSpeakerUiMode?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSpeakerMenuObservers the mode is $it")
            changeActivityVolumeStream(
                if (it == SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET) {
                    AudioManager.STREAM_VOICE_CALL
                } else AudioManager.STREAM_MUSIC
            )

            when (it) {
                SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET -> {
                    val text = if (recordFilterIsRecycle()) {
                        mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_small_speaker_red)
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                    mSpeakerMenuItem?.isEnabled = true
                }

                SpeakerStateManager.SPEAKER_ON_WITHIN_HEADSET -> {
                    /*don't be grey on androidS*/
                    val text = if (recordFilterIsRecycle()) {
                        val icon = if (BaseUtil.isAndroidSOrLater) {
                            com.soundrecorder.common.R.drawable.ic_big_speaker_black
                        } else {
                            com.soundrecorder.common.R.drawable.ic_big_speaker_gray
                        }
                        mSpeakerMenuItem?.setIcon(icon)
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                }

                SpeakerStateManager.SPEAKER_OFF_WITHIN_HEADSET -> {
                    /*don't be grey on androidS*/
                    val text = if (recordFilterIsRecycle()) {
                        val icon = if (BaseUtil.isAndroidSOrLater) {
                            com.soundrecorder.common.R.drawable.ic_small_speaker_red
                        } else {
                            com.soundrecorder.common.R.drawable.ic_small_speaker_gray
                        }
                        mSpeakerMenuItem?.setIcon(icon)
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                }

                SpeakerStateManager.SPEAKER_ON_WITHOUT_HEADSET -> {
                    val text = if (recordFilterIsRecycle()) {
                        mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_big_speaker_black)
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                    mSpeakerMenuItem?.isEnabled = true
                }

                else -> {
                    val text = if (recordFilterIsRecycle()) {
                        mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_big_speaker_black)
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                    mSpeakerMenuItem?.isEnabled = true
                }
            }
        }

        speakerModeController?.mIsSpeakerOn?.observe(viewLifecycleOwner) {
            mViewModel?.playerController?.replay()
        }
        speakerModeController?.mPlayerCommand?.observe(viewLifecycleOwner) {
            if (it == SpeakerModeController.PLAYER_COMMAND_PAUSE) {
                mViewModel?.playerController?.pausePlay()
            }
        }
    }

    private fun changeActivityVolumeStream(streamType: Int? = AudioManager.STREAM_MUSIC) {
        DebugUtil.d(TAG, "changeActivityVolumeStream  $streamType")
        activity?.volumeControlStream = streamType ?: AudioManager.STREAM_MUSIC
    }

    private fun handleMarkAdd(v: View) {
        if (mViewModel?.checkLoadAmpFinished() != true) {
            // 波形还未解析出来，不能使用标记功能
            DebugUtil.d(TAG, "checkLoadAmpFinished false")
            return
        }
        val popup = COUIPopupListWindow(activity)
        popup.setUseBackgroundBlur(false)
        popup.itemList = markMenuList
        mPictureMarkHelper?.saveSelectMarkTime()
        var select = false
        popup.setOnItemClickListener { _, _, position, _ ->
            DebugUtil.d(TAG, "handleMarkAdd $position")
            if (position == MENU_POSITION_0) {
                DebugUtil.d(TAG, "onClick layout_mark_activity:")
                val markMetaData = MarkMetaData("", "", currentTimeMillis = mPictureMarkHelper?.getSelectMarkTime() ?: 0, -1, -1)
                val result = mViewModel?.addMark(false, markMetaData) ?: -1
                if (result >= 0) {
                    getAudioFragment()?.mBinding?.waveRecyclerview?.addMarkData =
                        mViewModel?.markHelper?.getMark(result)
                }
                BuryingPoint.addClickPlayTextMark()
                popup.dismiss()
            }
        }
        popup.setSubMenuClickListener(AdapterView.OnItemClickListener { _, _, position, _ ->
            DebugUtil.d(TAG, "onClick handleMarkAdd subMenuClickListener position $position")
            select = true
            mPictureMarkHelper?.handleSelectPictureMark(position)
            popup.dismiss()
        })
        popup.setOnDismissListener {
            if (!select) {
                mPictureMarkHelper?.dismissPictureMark()
            }
        }
        popup.show(v)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.img_mark_add -> handleMarkAdd(v)

            R.id.img_forward -> {
                if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用快进功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                BuryingPoint.addFastForward()
                mViewModel?.forwardOrBackWard(true)
            }

            R.id.img_backward -> {
                if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用快退功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                BuryingPoint.addFastBack()
                mViewModel?.forwardOrBackWard(false)
            }

            R.id.img_mark_list -> {
                val markListShowState = mViewModel?.isShowMarkList?.value.let { if (it == null) true else !it }
                mViewModel?.isShowMarkList?.postValueSafe(markListShowState)
            }

            R.id.red_circle_icon -> {
                if (recordFilterIsRecycle() && !PermissionUtils.hasAllFilePermission()) {
                    activity?.let {
                        PermissionDialogUtils.showPermissionAllFileAccessDialog(
                            it,
                            object : PermissionDialogUtils.PermissionDialogListener {
                                override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                                    if (isOk) {
                                        PermissionUtils.goToAppAllFileAccessConfigurePermissions(it)
                                    }
                                }
                            })
                        return
                    }
                }
                if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用播放功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return
                }
                mViewModel?.recordControlClick()
            }
        }
    }

    fun handleTransferText() {
        if (!ClickUtils.isQuickClick()) {
            DebugUtil.d(TAG, "onClick layout_transfer_text_activity: ${mPlaybackConvertViewModel?.mConvertStatus?.value}")
            clickTransferTextSelectTab()
            if (mPlaybackConvertViewModel?.mConvertStatus?.value == null) {
                mPlaybackConvertViewModel?.mNeedTransConvert = true
            } else {
                mPlaybackConvertViewModel?.mConvertStatus?.value?.let {
                    if (it in intArrayOf(CONVERT_STATUS_INIT, CONVERT_STATUS_CANCEL, CONVERT_STATUS_USERTIMEOUT)) {
                        isClickAiTitle = false
                        mConvertManagerImpl?.convertStartClickHandle()
                    }
                }
            }
        }
    }

    private fun processExport(anchor: View?) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        getConvertFragment()?.mConvertManagerImpl?.processExport2(
            anchor,
            shareDialogDismissListener,
            childFragmentManager
        )
    }

    private fun showRecoverDialog() {
        if (mRecoverDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "mDeleteDialog is showing")
            return
        }
        mRecoverDialog =
            activity?.let {
                RecoverFileDialog(
                    it,
                    getString(com.soundrecorder.common.R.string.recycle_tips_restore_record_title),
                    getString(com.soundrecorder.common.R.string.recycle_delete_recover)
                )
            }
        mRecoverDialog?.mOnFileRecoverListener = object : OnRecoverFileListener {
            override fun onRecoverFileResult(deleteSuccess: Boolean) {
                if (deleteSuccess) {
                    onRecoverRecordSuccess()
                }
            }

            override fun provideRecoverRequestCode(): Int {
                return REQUEST_CODE_SYS_RECOVER_AUTH
            }
        }
        mRecoverDialog?.mHideListener = OnDismissListener {
            if (mRecoverDialog != null) {
                mViewModel?.mNeedShowRecoverDialog?.postValue(false)
            }
        }
        mRecoverDialog?.showRecoverDialog(getOperaRecord())
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_RECYCLE_RECOVER] = RecorderUserAction.VALUE_MORE_DELETE
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_PLAY_MORE, RecorderUserAction.EVENT_PLAY_MORE,
            eventInfo, false
        )
    }

    private fun onRecoverRecordSuccess() {
        notifyRefreshRecordList()
        browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
        //埋点
        BuryingPoint.addPlayRecycleRecoverSuccess()
    }

    private fun showDeleteDialog(isCloudOn: Boolean, isRecycle: Boolean) {
        DebugUtil.d(TAG, "showDeleteDialog, isRecycle:$isRecycle")
        if (mDeleteDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "mDeleteDialog is showing")
            return
        }
        val activity = activity ?: return
        mDeleteDialog = DeleteFileDialog(
            activity, getString(com.soundrecorder.common.R.string.record_delete_title),
            getDeleteDialogMessage(isCloudOn, isRecycle), getString(com.soundrecorder.common.R.string.delete)
        )
        mDeleteDialog?.mOnFileDeleteListener = object : OnFileDeleteListener {
            override fun onDeleteFileResult(deleteSuccess: Boolean) {
                if (deleteSuccess) {
                    VibrateUtils.vibrate(activity)
                    onDeleteRecordSuccess(isRecycle)
                }
            }

            override fun provideDeleteRequestCode(): Int {
                return REQUEST_CODE_SYS_DELETE_AUTH
            }
        }
        mDeleteDialog?.mHideListener = OnDismissListener {
            if (mDeleteDialog != null) {
                if (isRecycle) {
                    mViewModel?.mNeedShowRecycleDeleteDialog?.postValue(false)
                } else {
                    mViewModel?.mNeedShowDeleteDialog?.postValue(false)
                }
            }
        }

        mDeleteDialog?.showDeleteDialog(getOperaRecord(), isRecycle)
        // 埋点
        val eventInfoDelete: MutableMap<String?, String?> = HashMap()
        if (isRecycle) {
            eventInfoDelete[RecorderUserAction.KEY_RECYCLE_DELETE] = RecorderUserAction.VALUE_MORE_DELETE
        } else {
            eventInfoDelete[RecorderUserAction.KEY_MORE_DELETE] = RecorderUserAction.VALUE_MORE_DELETE
        }
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_PLAY_MORE, RecorderUserAction.EVENT_PLAY_MORE,
            eventInfoDelete, false
        )
    }

    private fun getDeleteDialogMessage(isCloudOn: Boolean, isRecycle: Boolean): String {
        val message: String = if (isRecycle) {
            resources.getString(com.soundrecorder.common.R.string.the_record_will_be_deleted_from_device)
        } else {
            if (isCloudOn) { //开启云同步时
                resources.getString(com.soundrecorder.common.R.string.recycle_tips_cloud_recently_delete_record)
            } else {
                resources.getString(com.soundrecorder.common.R.string.recycle_tips_recently_delete_record)
            }
        }
        return message
    }

    private fun showDetailDialog() {
        val context = context ?: return
        val inflater = LayoutInflater.from(context)
        val view = inflater.inflate(R.layout.layout_playback_detail, null)
        val name = view.findViewById<TextView>(R.id.name)
        val time = view.findViewById<TextView>(R.id.time)
        val length = view.findViewById<TextView>(R.id.length)
        val path = view.findViewById<TextView>(R.id.path)
        val pathString: String?
        val pathStart = BaseUtil.getPhoneStorageDir(BaseApplication.getAppContext())
        val sdPathStart = BaseUtil.getSDCardStorageDir(BaseApplication.getAppContext())
        pathString = if (pathStart != null && (mViewModel?.playPath?.value?.startsWith(pathStart) == true)) {
            resources.getString(
                if (FeatureOption.IS_PAD) {
                    com.soundrecorder.common.R.string.device_phone_storage
                } else {
                    com.soundrecorder.common.R.string.phone_storage
                },
                mViewModel?.playPath?.value?.replace(pathStart, "")
            )
        } else if (mViewModel?.playPath?.value?.startsWith(sdPathStart) == true) {
            resources.getString(
                com.soundrecorder.common.R.string.sd_card_storage,
                mViewModel?.playPath?.value?.replace(sdPathStart, "")
            )
        } else {
            mViewModel?.playPath?.value
        }
        name.text = mViewModel?.playName?.value
        val date = Date(
            RecorderDBUtil.getInstance(BaseApplication.getAppContext()).getCreateTimeByPath(
                mViewModel?.recordId
                    ?: -1, mViewModel?.playName?.value?.endsWith(".amr") ?: false
            )
        )
        time.text = RecorderICUFormateUtils.formatDateTime(date)
        length.text = FileUtils.getSizeDescription(
            FileUtils.getFileSize(
                MediaDBUtils.genUri(
                    mViewModel?.recordId
                        ?: -1
                )
            )
        )
        path.text = pathString

        COUIAlertDialogBuilder(context).apply {
            setView(view)
            setTitle(com.soundrecorder.common.R.string.talkback_detail)
            setNegativeButton(com.soundrecorder.base.R.string.close) { _, _ ->
                mDeleteDialog?.dismiss()
            }
            setOnDismissListener {
                if (mDetailDialog != null) {
                    mViewModel?.mNeedShowDetailDialog?.postValue(false)
                }
            }
            setBlurBackgroundDrawable(true)
            mDetailDialog = show()
            mDetailDialog?.getButton(DialogInterface.BUTTON_NEGATIVE)?.setTextColor(
                resources.getColor(
                    com.support.appcompat.R.color.coui_color_label_theme_blue,
                    null
                )
            )
        }
        val eventInfoDetail: MutableMap<String?, String?> = HashMap()
        eventInfoDetail[RecorderUserAction.KEY_PLAY_MORE_DETAIL] = RecorderUserAction.VALUE_PLAY_MORE_DETAIL
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_PLAY_MORE, RecorderUserAction.EVENT_PLAY_MORE,
            eventInfoDetail, false
        )
    }

    private fun showRenameDialog(text: String? = null) {
        if (mRenameDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "mRenameDialog is showing")
            return
        }
        val activity = activity ?: return
        val queryUri = MediaDBUtils.BASE_URI
        var cursor: Cursor? = null
        val where: String = MediaStore.Audio.Media._ID + "=?"
        var mediaRecord: Record? = null
        try {
            cursor = context?.contentResolver?.query(queryUri, null, where, arrayOf(mViewModel?.recordId.toString()), null)
            cursor?.let {
                if (it.moveToNext()) {
                    mediaRecord = Record(
                        cursor,
                        Record.TYPE_FROM_MEDIA
                    )
                }
            }
        } catch (ignored: Exception) {
            DebugUtil.e(TAG, "cursor is exception$ignored")
        } finally {
            cursor?.close()
        }

        val content = if (text == null) {
            var lastIndex = -1
            if (!TextUtils.isEmpty(mediaRecord?.displayName)
                && (mediaRecord?.displayName?.lastIndexOf(".").also { lastIndex = it ?: 0 } ?: 0) > 0
            ) {
                val content = mediaRecord?.displayName?.substring(0, lastIndex) ?: ""
                DebugUtil.i(TAG, "showRenameDialog, mRenameDialog content is $content")
                content
            } else {
                return
            }
        } else {
            text
        }

        mRenameDialog = RenameFileDialog(activity, RenameFileDialog.FROM_PLAYBACK_MORE, content, object : PositiveCallback {
            override fun callback(displayName: String?, path: String?) {
                DebugUtil.i(TAG, "rename callback displayName: $displayName, path: $path")
                var index = -1
                if (path == null || path.lastIndexOf(".").also { index = it } == -1) {
                    return
                }
                val recordName = displayName + path.substring(index)
                DebugUtil.i(TAG, "setValue: recordName = $recordName")
                mViewModel?.renameRecord(path, recordName)
                summaryInterface?.onRecordNameStatusChange(true, displayName)
                mRenameDialog?.resetOperating()
                notifyRefreshRecordList()
                BuryingPoint.addActionForPlaybackRenameSuccess()
            }
        }).apply {
            this.requestCode = REQUEST_CODE_SYS_RENAME_AUTH
            this.mediaRecord = mediaRecord
        }
        mRenameDialog?.show()
        BuryingPoint.addActionForPlaybackRename()
    }

    private fun showPlaySettingDialog() {
        if (mViewModel == null) {
            return
        }
        if (bottomSheetDialogFragment?.dialog?.isShowing == true) {
            DebugUtil.i(TAG, "bottomSheetDialogFragment is showing")
            return
        }
        bottomSheetDialogFragment = COUIBottomSheetDialogFragment()
        val dialogFragment = PlaySettingDialogFragment()
        bottomSheetDialogFragment?.setMainPanelFragment(dialogFragment)
        if (isAdded) {
            bottomSheetDialogFragment?.show(childFragmentManager, PlaySettingDialogFragment.FRAGMENT_TAG)
        }
        mViewModel?.mNeedShowSpeedDialog?.value = false
    }

    private fun configToolbar() {
        binding.toolbar.clearMenu()
        if (recordFilterIsRecycle()) {
            showRecycleToolBar()
        } else {
            showNormalToolbar()
        }

        setNavigationIcon()
        binding.toolbar.setNavigationOnClickListener {
            if (isFromAppCard()) {
                activity?.finish()
            } else {
                browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
            }
        }
    }

    /**
     * 从小布建议卡跳转过来
     */
    private fun isFromAppCard(): Boolean {
        val from = activity?.intent?.getStringExtra("jumpFrom") ?: return false
        return from == "AppCard"
    }

    private fun showNormalToolbar() {
        binding.toolbar.inflateMenu(R.menu.menu_play_back)

        binding.toolbar.menuView?.apply {
            this.setItemSpecialColor(
                R.id.delete,
                COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorError)
            )
        }
        mSpeakerMenuItem = binding.toolbar.menu.findItem(R.id.speaker)
        mPlaySettingMenuItem = binding.toolbar.menu.findItem(R.id.play_setting)
        mCutMenuItem = binding.toolbar.menu.findItem(R.id.cut)
        mSearchMenuItem = binding.toolbar.menu.findItem(R.id.search)
        mConvertRoleMenuItem = binding.toolbar.menu.findItem(R.id.convert_role)

        lifecycleScope.launchWhenResumed {
            binding.toolbar.menu.apply {
                ensureConvertSearchAnimView(this)
                setShowSmartNameMenuView()
                if (FeatureOption.IS_PAD) {
                    mSpeakerMenuItem?.isVisible = false
                }
                this.children.forEach {
                    it.setOnMenuItemClickListener { menuItem ->
                        onOptionsItemSelected(menuItem)
                    }
                }
            }
        }
    }

    private fun setShowSmartNameMenuView() {
        mSupportSmartName = Injector.injectFactory<BrowseFileInterface>()?.isSupportSmartName(mBrowseFileActivityViewModel) ?: false
        DebugUtil.d(TAG, "setShowSmartNameMenuView, supportSmartName:$mSupportSmartName")
        if (mSupportSmartName) {
            binding.toolbar.menu.findItem(R.id.rename).isVisible = false
            binding.toolbar.menu.findItem(R.id.rename_new).isVisible = true
            binding.toolbar.menuView?.apply {
                setOverflowMenuListener { popup ->
                    mToolbarOverflowPopupWindow = popup
                    val mainList = popup.itemList
                    DebugUtil.d(TAG, "configToolbar mainList:${mainList.size}")
                    for (item in mainList) {
                        if (item.id == R.id.rename_new) {
                            for (subItem in item.subMenuItemList) {
                                subItem.stateIcon = ResourcesCompat.getDrawable(
                                    resources,
                                    R.drawable.menu_transparent_selector,
                                    activity?.theme
                                )
                            }
                        } else if (item.id == R.id.delete) {
                            item.itemType = PopupListItem.MENU_ITEM_TYPE_ALERT
                        }
                    }
                }
            }
        } else {
            binding.toolbar.menu.findItem(R.id.rename).isVisible = true
            binding.toolbar.menu.findItem(R.id.rename_new).isVisible = false
            binding.toolbar.menuView?.apply {
                setOverflowMenuListener { popup ->
                    mToolbarOverflowPopupWindow = popup
                    val mainList = popup.itemList
                    DebugUtil.d(TAG, "showSmartNameMenuView false configToolbar mainList:${mainList.size}")
                    for (item in mainList) {
                        if (item.id == R.id.delete) {
                            item.itemType = PopupListItem.MENU_ITEM_TYPE_ALERT
                        }
                    }
                }
            }
        }
    }

    private fun showRecycleToolBar() {
        binding.toolbar.inflateMenu(R.menu.menu_play_back_recycle)

        mSpeakerMenuItem = binding.toolbar.menu.findItem(R.id.speaker)
        mRecoverMenuItem = binding.toolbar.menu.findItem(R.id.recycle_recover)
        mDeleteMenuItem = binding.toolbar.menu.findItem(R.id.recycle_delete)

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                binding.toolbar.menu.apply {
                    if (FeatureOption.IS_PAD) {
                        mSpeakerMenuItem?.isVisible = false
                    }
                    this.children.forEach {
                        it.setOnMenuItemClickListener { menuItem ->
                            onOptionsItemSelected(menuItem)
                        }
                    }
                }
            }
        }
    }

    private fun ensureConvertSearchAnimView(menu: Menu?) {
        if (!FunctionOption.IS_SUPPORT_CONVERT_SEARCH) {
            return
        }
        if (menu != null) {
            val mSearchItem = menu.findItem(R.id.search)
            if (searchAnimView == null) {
                searchAnimView = mSearchItem?.actionView as? COUISearchBar
            }
            mPlaybackConvertViewModel?.let {
                searchAnimView?.apply {
                    setAtBehindToolBar(binding.toolbar, Gravity.TOP, mSearchItem)
                    searchEditText.filters =
                        arrayOf<InputFilter>(InputFilter.LengthFilter(SEARCH_INPUT_MAX_LENGTH))
                    searchEditText.hint =
                        BaseApplication.getAppContext().resources.getString(com.soundrecorder.common.R.string.search_convert_content)
                    functionalButton?.setOnClickListener {
                        getConvertSearchFragment()?.onClickCancel()
                    }
                }
            }
            searchAnimView?.searchEditText?.let {
                it.setText(mPlaybackConvertViewModel?.mConvertSearchValue ?: "")
                it.addTextChangedListener(object : android.text.TextWatcher {
                    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                    }

                    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                        getConvertSearchFragment()?.onQueryTextChange(s?.toString())
                        browseFileApi?.onConvertSearchStateChanged(
                            activity, mPlaybackConvertViewModel?.mIsInConvertSearch?.value ?: false,
                            !s.isNullOrBlank()
                        ) {
                            findConvertSearchFragment()?.onClickCancel()
                        }
                    }

                    override fun afterTextChanged(s: Editable?) {
                    }
                })
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (mPlaybackConvertViewModel?.mIsInConvertSearch?.value == true) {
            DebugUtil.i(TAG, "onOptionsItemSelected in convert search ,ignore menu item click event")
            return super.onOptionsItemSelected(item)
        }
        when (item.itemId) {
            android.R.id.home -> browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
            R.id.speaker -> {
                if (mViewModel?.checkLoadAmpFinished() != true) {
                    // 波形还未解析出来，不能使用听筒切换功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false return")
                    return false
                }
                browseFileApi?.getViewModelSpeakerModeController<SpeakerModeController>(mBrowseFileActivityViewModel)
                    ?.performSpeakerMenuItemClick(SpeakerModeController.PLAYER_SPEAKER_PLAYBACK)
            }

            R.id.recycle_delete -> mViewModel?.mNeedShowRecycleDeleteDialog?.value = true
            R.id.recycle_recover -> mViewModel?.mNeedShowRecoverDialog?.value = true
            R.id.export -> {
                if (mViewModel?.hasConvertContent?.value == true) {
                    mViewModel?.mNeedShowShareDialog?.value = true
                } else {
                    if (ClickUtils.isQuickClick()) {
                        return super.onOptionsItemSelected(item)
                    }
                    val activity = activity ?: return false
                    FileDealUtil.sendRecordFile(activity, mViewModel?.recordId ?: -1, null)
                    DebugUtil.d(TAG, "sendRecordFile")
                }
                BuryingPoint.addClickMoreShare()
            }

            R.id.move -> {
                browseFileApi?.showGroupChooseFragment(
                    this@PlaybackContainerFragment,
                    mViewModel?.getRecord()
                )
            }

            R.id.rename -> {
                showRenameDialog()
            }

            R.id.rename_new -> {
                if (mSupportSmartName) {
                    showSmartNamePopupWindow()
                } else {
                    showRenameDialog()
                }
            }

            R.id.detail -> mViewModel?.mNeedShowDetailDialog?.value = true
            R.id.delete -> mViewModel?.mNeedShowDeleteDialog?.value = true
            R.id.set_ringtone -> {
                BuryingPoint.addClickSetRingtone(RecorderUserAction.VALUE_SET_RINGTONE_FROM_MORE)
                SendSetUtil.setAs(mViewModel?.recordId ?: -1, activity)
            }
            R.id.search -> {
                showSearch()
            }
            R.id.play_setting -> {
                if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用倍速功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return true
                }
                BuryingPoint.addPlaySetting()
                mViewModel?.mNeedShowSpeedDialog?.postValue(true)
            }
            R.id.cut -> {
                if (mViewModel?.ampList?.value.isNullOrEmpty()) {
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.amplitues_not_ready
                    )
                    return true
                }
                activity?.let { act ->
                    editRecordApi?.createEditRecordIntent(act)?.let {
                        it.putExtra("playPath", mViewModel?.playPath?.value)
                        it.putExtra("playName", mViewModel?.playName?.value)
                        it.putExtra("recordType", mViewModel?.recordType?.value)
                        it.putExtra("recordId", mViewModel?.recordId)
                        it.putExtra("isFromOtherApp", mViewModel?.mIsFromOtherApp)
                        it.putExtra("mimeType", mViewModel?.mMimeType?.value)
                        it.putParcelableArrayListExtra(
                            "mark", ArrayList(
                                mViewModel?.getMarkList()?.value
                                    ?: arrayListOf()
                            )
                        )
                        mStartEditLauncher?.launch(it)
                        activity?.overridePendingTransition(
                            com.soundrecorder.common.R.anim.enter_bottom,
                            com.soundrecorder.common.R.anim.exit_top
                        )
                        mViewModel?.playerController?.pausePlay()
                        CuttingStaticsUtil.addPlayMoreTrim()
                    }
                }
            }
            R.id.convert_role -> {
                if (isOnConvert()) {
                    getConvertFragment()?.switchSpeakerRoleState()
                }
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun showSearch() {
        if (ClickUtils.isFastDoubleClick()) {
            DebugUtil.i(TAG, "showSearch isFastDoubleClick ")
            return
        }

        if (isOnConvert()) {
            getConvertFragment()?.convertSearch()
        }
    }

    private fun showSmartNamePopupWindow() {
        mToolbarOverflowPopupWindow?.apply {
            setSubMenuClickListener(AdapterView.OnItemClickListener { _, _, position, _ ->
                val mainList = itemList
                DebugUtil.d(TAG, "onOptionsItemSelected, mainList:${mainList.size}")
                for (itemGroup in mainList) {
                    if (itemGroup.id == R.id.rename_new) {
                        mSubMenuCheckedPosition = position
                        when (mSubMenuCheckedPosition) {
                            0 -> showRenameDialog()
                            1 -> {
                                //智能命名
                                DebugUtil.d(TAG, "subMenuClick, rename ai:$position")
                                isClickAiTitle = true
                                val mediaIdList = mutableListOf<Long>()
                                mViewModel?.recordId?.let { mediaIdList.add(it) }
                                if (!PermissionUtils.hasAllFilePermission()) {
                                    showPermissionAllFileDialog(true, mediaIdList)
                                    dismiss()
                                    return@OnItemClickListener
                                }
                                startConvertAndSmartName(mediaIdList)
                            }
                        }
                        dismiss()
                    }
                }
            })
        }
    }

    private fun startConvertAndSmartName(mediaIdList: MutableList<Long>) {
        if (PermissionUtils.hasAllFilePermission()) {
            initSmartNameManagerImpl()
            mSmartNameMangerImpl?.convertStartSmartNameClickHandle(activity, mediaIdList, PAGE_FROM_PLAYBACK)
            mViewModel?.clearNeedSmartNameMedias()
        }
    }

    private fun showPermissionAllFileDialog(needSmartName: Boolean = false, selectedMediaIdList: MutableList<Long>? = null) {
        if (ClickUtils.isFastDoubleClick()) {
            return
        }
        activity?.let {
            mFilePermissionDialog = PermissionDialogUtils.showPermissionAllFileAccessDialog(
                it,
                object : PermissionDialogUtils.PermissionDialogListener {
                    override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                        DebugUtil.d(TAG, "showPermissionAllFileDialog, isOk:$isOk, permissions:$permissions")
                        if (isOk) {
                            if (needSmartName) {
                                mViewModel?.addNeedSmartNameMedias(selectedMediaIdList)
                            }
                            PermissionUtils.goToAppAllFileAccessConfigurePermissions(it)
                        }
                    }
                })
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        DebugUtil.i(TAG, "onActivityResult: request code $requestCode result code $resultCode")
        when (requestCode) {
            REQUEST_CODE_SYS_RENAME_AUTH -> {
                if (resultCode == Activity.RESULT_OK) {
                    val suffix = mViewModel?.getRecordSuffix()
                    val renameContent = mRenameDialog?.getRenameContent()
                    if (FileDealUtil.renameAgain(mViewModel?.playerController?.getPlayUri(), renameContent, suffix)) {
                        mViewModel?.renameRecordByCore2Full(renameContent)
                        CloudStaticsUtil.addCloudLog(TAG, "renameAgain, ${mViewModel?.playName?.value} renameTo $renameContent success")
                    }
                }
                mRenameDialog?.resetOperating()
            }

            REQUEST_CODE_SYS_DELETE_AUTH -> {
                if (resultCode == Activity.RESULT_OK) {
                    responseDeleteBatch()
                }
                mDeleteDialog?.resetOperating()
            }

            REQUEST_CODE_SYS_RECOVER_AUTH -> {
                if (resultCode == Activity.RESULT_OK) {
                    responseRecoverBatch()
                }
                mRecoverDialog?.resetOperating()
            }

            Constants.EXPORT_TO_NOTE_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.save_success
                    )
                }
            }

            Constants.RESULT_CODE_PLAYBACK_SUMMARY -> {
                // 通话记录ID没有摘要笔记
                if (resultCode == Constants.NOTES_RESULT_CODE_ERROR) {
                    mViewModel?.summaryNoteId?.let {
                        browseFileApi?.getDeleteSummaryNoteIdLiveData(mBrowseFileActivityViewModel)?.value = it
                    }
                    mViewModel?.deleteNoteData()
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.tip_summary_be_deleted
                    )
                }
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun responseRecoverBatch() {
        val activity = activity ?: return
        val result = FileDealUtil.recoveryRecord(activity, getOperaRecord(), REQUEST_CODE_SYS_RECOVER_AUTH)
        DeleteSoundEffectManager.getInstance().playDeleteSound()
        CloudStaticsUtil.addCloudLog(TAG, "responseDeleteBatch, delete ${mViewModel?.playName?.value} result=$result")
        onRecoverRecordSuccess()
    }

    private fun getOperaRecord(): Record {
        return if (mViewModel?.getRecord() != null) {
            mViewModel?.getRecord()!!
        } else {
            Record().apply {
                id = mViewModel?.recordId ?: -1
                data = mViewModel?.playPath?.value ?: ""
                recordType = mViewModel?.recordType?.value ?: 0
                recycleFilePath = mViewModel?.mPlayPath ?: mViewModel?.playPath?.value
                displayName = mViewModel?.playName?.value ?: ""
                setIsRecycle(recordFilterIsRecycle())
            }
        }
    }

    private fun responseDeleteBatch() {
        val activity = activity ?: return
        val id = mViewModel?.recordId ?: -1
        val result = FileDealUtil.deleteRecordDBBatch(activity, mViewModel?.playPath?.value, id)
        DeleteSoundEffectManager.getInstance().playDeleteSound()
        CloudStaticsUtil.addCloudLog(TAG, "responseDeleteBatch, delete ${mViewModel?.playName?.value} result=$result")
        onDeleteRecordSuccess(recordFilterIsRecycle())
    }

    private fun onDeleteRecordSuccess(isRecycle: Boolean) {
        notifyRefreshRecordList()
        browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
        if (isRecycle) {
            BuryingPoint.addPlayRecycleDeleteSuccess()
        } else {
            seedingApi?.sendRecordDeleteEvent()
            BuryingPoint.addPlayMoreDeleteSuccess()
        }
        if (isFromAppCard()) {
            startToBrowseFile()
        }
    }

    private fun notifyRefreshRecordList() {
        val intent = Intent(RecordFileChangeNotify.FILE_UPDATE_ACTION)
        intent.putExtra(Constants.FRESH_FLAG, true)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    private fun stopObserver() {
        mViewModel?.playerController?.playerState?.removeObserver(mPlayStateChangeObserver)
    }

    private fun registerReceivers() {
        MultiFileObserver.getInstance().addFileEventListener(this)
        registerConvertReceiver()
    }

    private fun unregisterReceivers() {
        MultiFileObserver.getInstance().removeFileEventListener(this)
        LocalBroadcastManager.getInstance(context ?: BaseApplication.getAppContext())
            .unregisterReceiver(convertReceiver)
    }

    override fun onFileObserver(event: Int, path: String?, allPath: String?) {
        when (event) {
            FileObserver.MOVED_FROM, FileObserver.DELETE,
            FileObserver.DELETE_SELF, FileObserver.MOVE_SELF -> {
                DebugUtil.d("$TAG-onFileObserver", "path:$path,allPath:$allPath")
                cancelAndFinish(path, allPath)
            }
        }
    }

    private fun isSmallWindow(): Boolean {
        return browseFileApi?.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.value == WindowType.SMALL
    }

    private fun cancelAndFinish(path: String?, allPath: String?) {
        val playPath = mViewModel?.playPath?.value
        if (!allPath.isNullOrEmpty() && !playPath.isNullOrEmpty() && playPath.startsWith(allPath)) {
            /*再有的外销手机上，fileObserver会先于OnActivityResult方法，所以在此处增加删除逻辑*/
            if (mDeleteDialog?.getOperating() == true) {
                responseDeleteBatch()
                return
            }
            /*中大屏下处理列表重命名音频文件场景*/
            if (!isSmallWindow()) {
                val mediaRecord = MediaDBUtils.getRecordFromMediaByUriId(MediaDBUtils.genUri(mViewModel?.recordId ?: -1))
                DebugUtil.i(TAG, "onFileObserver,media.name=${mediaRecord?.displayName},show.name=${mViewModel?.playName?.value}")
                /**媒体库有延迟，mediaRecord有可能查询到之前的信息，所以这里增加了两边name不一致的判断*/
                if ((mediaRecord != null) && (mViewModel?.playName?.value != mediaRecord.displayName)) {
                    mViewModel?.renameRecord(
                        mediaRecord.data,
                        mediaRecord.displayName
                    )
                    return
                }
            }
            DebugUtil.d("$TAG-onFileObserver", "path:$path,allPath:$allPath,playPath:$playPath")
            mViewModel?.cancelNotification()
            browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
            DebugUtil.d("$TAG-onFileObserver", "path:$path,allPath:$allPath,playPath:$playPath,结束")
        }
    }

    /**
     * 显示 “请稍后。。。” dialog
     * 当转文本的文件大于  Constants.SHOW_WAITING_DIALOG__THRESHOLD 的时候，保存到本地和分享需要弹窗
     * 弹窗显示不足1s的时候，需要延迟到1s之后再关闭
     */
    private fun showWaitingDialog(type: ShareType?) {
        val msgResId = when (type) {
            is ShareTypeNote -> com.soundrecorder.common.R.string.is_saving
            is ShareTypeLink -> com.soundrecorder.common.R.string.generating
            else -> com.soundrecorder.common.R.string.waiting
        }
        val activity = activity ?: return
        if (mLoadingDialog == null) {
            mLoadingDialog = LoadingDialog(activity)
        }
        if (mLoadingDialog?.isActivityNull() == true) {
            mLoadingDialog?.resetActivity(activity)
        }
        mLoadingDialog?.show(msgResId)
        mViewModel?.setNeedRestoreWaitingDialog(false)
    }

    /**
     * 关闭waitingDialog并且显示分享面板
     */
    private fun dismissDialog() {
        DebugUtil.i(TAG, "dismissDialogDoExport...")
        if (mLoadingDialog?.isShowing() == true) {
            mLoadingDialog?.dismiss()
        }
    }

    override fun onDestroyView() {
        DebugUtil.e(TAG, "onDestroyView")
        cacheDialogStatus()
        releaseDialog()
        releasePopupWindow()
        mPageChangeCallback?.let { binding.viewpager.unregisterOnPageChangeCallback(it) }
        FollowDialogRestoreUtils.releaseFollowDialogRunnable(activity?.window?.decorView)
        disableDialog.dismissWhenShowing()
        mFilePermissionDialog.dismissWhenShowing()
        super.onDestroyView()
        mLoadingViewControl = null
        seekBar = null
        disableDialog = null
        browseFileApi?.getViewModelAnimRunning(mBrowseFileActivityViewModel)?.let {
            if (it.value == true) {
                // 将小屏动效置为false，避免动效执行过程中，重建导致该值没有被重置
                it.value = false
                DebugUtil.i(TAG, "getViewModelAnimRunning set false")
            }
        }
        mViewModel?.isImmersiveAnimationRunning = false
        if (immersiveAnimationHelperInitial == true) {
            immersiveAnimationHelper.release()
        }
        mViewModel?.playerController?.removeTimerTickListener(mTimerTickCallback)
        binding.buttonPanel.clButtonPanel.unObserveHeightChange()
    }

    private fun releasePopupWindow() {
        if (mToolbarOverflowPopupWindow?.isShowing == true) {
            mToolbarOverflowPopupWindow?.dismiss()
        }
        mToolbarOverflowPopupWindow = null
    }

    override fun onDestroy() {
        DebugUtil.d(TAG, "onDestroy: isFinishing = ${activity?.isFinishing}, isRemoving = $isRemoving")
        if ((activity?.isFinishing == true) || isRemoving) {
            checkIfNeedTrigCloudSync()
        }
        unregisterReceivers()
        stopObserver()
        mViewModel?.let {
            it.isAddPictureMarkingCallback = null
            it.setShareCallBack(null)
            it.clearLifecycle(this)
        }
        if ((activity?.isFinishing == true) || isRemoving) {
            mViewModel?.cancelNotification()
        }
        mSmartNameMangerImpl?.release()
        mSmartNameMangerImpl = null
        super.onDestroy()
        mPictureMarkHelper = null
        val isFinish = activity?.isFinishing == true
        if (isFinish) {
            mViewModel?.playerController?.releasePlay()
        }
    }


    private fun cacheDialogStatus() {
        mViewModel?.mNeedShowRenameDialog?.value = mRenameDialog?.isShowing() ?: false
        mViewModel?.setNeedRestoreWaitingDialog(mLoadingDialog?.isShowing() ?: false)
        mViewModel?.mNeedShowSelectPictureDialog?.value = mPictureMarkHelper?.isDialogShowing()
        mViewModel?.mNeedShowMarkRenameDialog?.value = mMarkListAdapter?.isRenameMarkDialogShowing ?: false
        mViewModel?.mNeedShowMarkDeleteDialog = mMarkListAdapter?.isDeleteMarkDialogShowing ?: false
    }

    private fun dismissMenuPop() {
        mMarkListAdapter?.dismissMenuPop()
    }

    private fun releaseDialog() {
        mLoadingDialog?.dismiss()
        mLoadingDialog = null
        mDeleteDialog?.release()
        mDeleteDialog = null
        mDetailDialog?.dismiss()
        mDetailDialog = null
        mViewModel?.mRenameEditText = mRenameDialog?.getNewContent().toString()
        mRenameDialog?.release()
        mRenameDialog = null
        releaseMarkListDialogs()
    }

    private fun releaseMarkListDialogs() {
        mMarkListAdapter?.let {
            if (it.isRenameMarkDialogShowing) {
                DebugUtil.i(TAG, "releaseDialogs mRenameMarkDialog")
                mViewModel?.mRenameMarkEditText = it.renameMarkDialog?.getNewContent().toString()
                mViewModel?.mMarkRenameData = it.mRenameMark
                it.dismissRenameDialog()
            }
            if (it.isDeleteMarkDialogShowing) {
                mViewModel?.mMarkDeleteData = it.mDeleteMark
                it.dismissDeleteDialog()
            }
            it.setOnShouldShowMenuPopListener(null)
        }
        dismissMenuPop()
    }

    private val mPlayStateChangeObserver = Observer<Int> {
        DebugUtil.i(TAG, "mPlayerState changed $it")
        mViewModel?.let { vm ->
            vm.markEnable.value = vm.isMarkEnabled()
        }
        when (it) {
            PlayStatus.PLAYER_STATE_PLAYING -> {
                binding.buttonPanel.redCircleIcon.switchPauseState()
                mViewModel?.showNotification()
            }

            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> binding.buttonPanel.redCircleIcon.switchPauseState()

            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> binding.buttonPanel.redCircleIcon.switchPlayState()

            else -> {
                binding.buttonPanel.redCircleIcon.switchPlayState()
                mViewModel?.cancelNotification()
            }
        }
    }

    override fun doPictureMark(pictureMarkMetaData: MarkMetaData) {
        mViewModel?.addMark(false, pictureMarkMetaData)
    }

    override fun doSingleOrMultiPictureMarkEnd(operateCancel: Boolean, fromSource: Int) {
        when (fromSource) {
            IPictureMarkDelegate.SOURCE_ALBUM -> {
                if (operateCancel) {
                    BuryingPoint.playingAddPictureByAlbumCancel()
                } else {
                    BuryingPoint.playingAddPictureByAlbumOk()
                }
            }

            IPictureMarkDelegate.SOURCE_CAMERA -> {
                if (operateCancel) {
                    BuryingPoint.playingAddPictureByCameraCancel()
                } else {
                    BuryingPoint.playingAddPictureByCameraNumber()
                }
            }
        }
    }

    override fun releaseMarks(isFinishing: Boolean) {
        mViewModel?.getMarkList()?.value?.forEach {
            it.release(isFinishing)
        }
    }

    override fun supportPictureMarkSource(): List<Int> = listOf(IPictureMarkDelegate.SOURCE_CAMERA, IPictureMarkDelegate.SOURCE_ALBUM)

    override fun getPlayerCurrentTimeMillis(): Long {
        return mViewModel?.getCurrentTime() ?: -1L
    }

    override fun getMarkList(): List<MarkDataBean>? = mViewModel?.getMarkList()?.value

    override fun getDuration(): Long = mViewModel?.playerController?.getDuration() ?: -1L

    override fun restorePlayerStatePlaying(cachePlayerState: Int, hasNeedSpeakOff: Boolean): Int {
        when (cachePlayerState) {
            PlayStatus.PLAYER_STATE_PLAYING, PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                if (hasNeedSpeakOff && speakerModeController?.mIsSpeakerOn?.value == false) {
                    return PlayStatus.PLAYER_STATE_INIT
                }
                continuePlay()
            }
        }
        return PlayStatus.PLAYER_STATE_INIT
    }

    override fun onActivityLaunched(sourceType: Int) {
        when (sourceType) {
            IPictureMarkDelegate.SOURCE_ALBUM -> {
                activity?.overridePendingTransition(
                    com.soundrecorder.common.R.anim.enter_bottom,
                    com.soundrecorder.common.R.anim.exit_top
                )
            }

            IPictureMarkDelegate.SOURCE_CAMERA -> {
                activity?.overridePendingTransition(
                    com.support.appcompat.R.anim.coui_open_slide_enter,
                    com.support.appcompat.R.anim.coui_open_slide_exit
                )
            }
        }
    }

    private fun continuePlay() {
        if (mViewModel?.playerController?.isWholePlaying() != true) {
            mViewModel?.playerController?.seekToPlay()
        }
    }

    override fun hasPlayerStatePlaying(): Int {
        return mViewModel?.playerController?.playerState?.value ?: PlayStatus.PLAYER_STATE_INIT
    }

    override fun beforeShowSelectPictureDialog() {
        mViewModel?.playerController?.pausePlay()
    }

    private fun checkIfNeedTrigCloudSync() {
        if (mViewModel?.mNeedTrigCloud == true) {
            cloudKitApi?.trigBackupNow(BaseApplication.getAppContext())
        }
    }

    private fun inAndOutAudioFragment() {
        val visible = binding.flConvertAudioContainer.isVisible
        if (visible) {
            val audioFragment = getAudioFragment()
            if (audioFragment?.isAdded != true) {
                DebugUtil.i(TAG, "add audio fragment")
                replaceFragmentByTag(
                    R.id.fl_convert_audio_container,
                    getAudioFragment(),
                    PlaybackAudioFragment.TAG
                )
            }
        } else {
            findFragment<PlaybackAudioFragment>(PlaybackAudioFragment.TAG)?.let { audioFragment ->
                DebugUtil.i(TAG, "remove audio fragment")
                removeFragment(audioFragment)
            } ?: run {
                DebugUtil.i(TAG, "remove audio fragment is null")
            }
        }
    }

    private fun inAndOutConvertSearchFragment(jumpToConvertSearch: Boolean) {
        if (jumpToConvertSearch) {
            // 保存搜索动效的高度
            getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()?.saveKeyWordViewHeight()

            binding.tabLayout.isVisible = false
            searchAnimView?.searchEditText?.setText(mPlaybackConvertViewModel?.mConvertSearchValue ?: "")
            binding.toolbar.post {
                searchAnimView?.showInToolBar()
            }
            // 取消右上角的更多弹窗
            binding.toolbar.postDelayed({
                binding.toolbar.dismissPopupMenus()
            }, TIP_SHOW_DELAY_TIME)

            replaceFragmentByTag(
                R.id.fl_convert_search_container,
                getConvertSearchFragment(),
                ConvertSearchFragment.TAG
            )
        } else {
            if (isAdded && childFragmentManager.findFragmentByTag(ConvertSearchFragment.TAG) != null) {
                // 退出搜索检测是否需要展示气泡
                getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()?.checkOutSearchShowBackOfLocation()
                removeFragment(getConvertSearchFragment())
            }
            searchAnimView?.apply {
                searchEditText.setText("")
                hideInToolBar()
            }
            binding.tabLayout.isVisible = checkShowTabLayout()
            // 退出搜索的动效
            getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()?.animSearchOut()
        }
    }

    private fun initiateWindowInsets(rootView: View) {
        val callback = object : DeDuplicateInsetsCallback() {
            override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                v.updatePadding(
                    top = stableStatusBarInsets.top, left = stableStatusBarInsets.left,
                    right = stableStatusBarInsets.right
                )
                binding.buttonPanel.root.updatePadding(bottom = stableStatusBarInsets.bottom)
                DebugUtil.i(TAG, "onApplyInsets stableStatusBarInsets = $stableStatusBarInsets")
                getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()?.dismissRenameSpeakerDialog()
                updateImmersiveNavigationHeight(stableStatusBarInsets.bottom)
                (mSummaryFragment as? IAISummaryInterface)?.setBottomMargin(stableStatusBarInsets.bottom)
            }
        }
        ViewCompat.setOnApplyWindowInsetsListener(rootView, callback)
    }

    fun isViewPager2IDLE(): Boolean = binding.viewpager.scrollState == ViewPager2.SCROLL_STATE_IDLE

    fun isOnConvert(): Boolean {
        if (mPlaybackConvertViewModel?.mIsInConvertSearch?.value == true) { // 处于文本搜索界面
            return false
        }
        return binding.viewpager.isVisible && (mViewModel?.mCurrentTabType?.value == TAB_TYPE_CONVERT)
    }

    fun onPrivacyPolicySuccess(type: Int) {
        when (type) {
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT -> {
                if (isClickAiTitle) {
                    initSmartNameManagerImpl()
                    mSmartNameMangerImpl?.doClickPermissionConvertOK(activity, mViewModel?.needSmartNameMediaList, PAGE_FROM_PLAYBACK)
                    mViewModel?.clearNeedSmartNameMedias()
                } else {
                    getConvertFragment()?.mConvertManagerImpl?.doClickPermissionConvertOK()
                }
                isClickAiTitle = false
            }

            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH -> getConvertFragment()?.mConvertManagerImpl?.doClickPermissionConvertSearchOK()
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        DebugUtil.i(TAG, "onSaveInstanceState")
        if (mViewModel?.isSupportConvert() == true) {
            outState.putBoolean(
                EKY_IS_IN_CONVERT_SEARCH,
                mPlaybackConvertViewModel?.mIsInConvertSearch?.value ?: false
            )
            outState.putString(
                KEY_IN_CONVERT_SEARCH_VALUE,
                mPlaybackConvertViewModel?.mConvertSearchValue ?: ""
            )
            outState.putInt(
                KEY_CONVERT_SEARCH_CURRENT_POS,
                mPlaybackConvertViewModel?.currentPos ?: 0
            )
            outState.putInt(
                KEY_CONVERT_SEARCH_LAST_POS,
                mPlaybackConvertViewModel?.lastPos ?: 0
            )
        }
        if (markListDialogFragment?.isAdded == true) {
            DebugUtil.e(TAG, "onSaveInstanceState is add")
            outState.putString(MARK_LIST_SHOW_STATE, MARK_LIST_SHOW)
        }
        super.onSaveInstanceState(outState)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mPictureMarkHelper?.onConfigurationChanged(newConfig)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        mPictureMarkHelper?.setRequestCodeX(requestCode)
        super.startActivityForResult(intent, requestCode)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int, options: Bundle?) {
        mPictureMarkHelper?.setRequestCodeX(requestCode)
        super.startActivityForResult(intent, requestCode, options)
    }

    fun getIPictureMarkDelegate(): IPictureMarkDelegate<MarkMetaData>? = mPictureMarkHelper

    override fun onBackPressed(): Boolean {
        if (mPlaybackConvertViewModel?.mIsInConvertSearch?.value == true) {
            mPlaybackConvertViewModel?.mIsInConvertSearch?.value = false
            return true
        }
        if (isFromAppCard()) {
            activity?.finish()
            return true
        } else {
            // 小屏下销毁播放页面
            if ((browseFileApi?.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.value == WindowType.SMALL)
                && (browseFileApi?.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel) != null)
            ) {
                browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
                return true
            }
        }
        return false
    }

    override fun pausePlay(clearNotification: Boolean) {
        mViewModel?.playerController?.pausePlay()
        if (clearNotification) {
            mViewModel?.cancelNotification()
        }
    }

    private fun recordFilterIsRecycle(): Boolean {
        return mViewModel?.isRecycle == true
    }

    override fun onUserChange() {
        mViewModel?.cancelNotification()
    }

    private fun registerConvertReceiver() {
        val isFromOtherApp = browseFileApi?.getViewModelIsFromOther(mBrowseFileActivityViewModel) ?: false
        if (ConvertSupportManager.isSupportConvert(!isFromOtherApp)) {
            DebugUtil.d(TAG, "registerConvertReceiver")
            val convertFilter = IntentFilter()
            convertFilter.addAction(NOTIFY_CONVERT_STATUS_UPDATE)
            convertFilter.addAction(NOTIFY_SMART_NAME_STATUS_UPDATE)
            LocalBroadcastManager.getInstance(context ?: BaseApplication.getAppContext())
                .registerReceiver(convertReceiver, convertFilter)
        }
    }

    /**
     * 切换沉浸态
     */
    private fun switchImmersive(isShowImmersive: Boolean) {
        if (isShowImmersive) {
            startImmersiveAnimation()
        } else {
            reverseImmersiveAnimation()
        }
    }

    /**
     * 进入沉浸态
     */
    private fun startImmersiveAnimation() {
        DebugUtil.i(TAG, "startImmersiveAnimation")
        //列表渐变遮住隐藏
        getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()
            ?.startImmersiveAnimation()
        //底部面板沉浸态动画
        immersiveAnimationHelper.startImmersiveAnimation(navigationHeight ?: 0, windowType)
    }

    /**
     * 退出沉浸态
     */
    private fun reverseImmersiveAnimation() {
        DebugUtil.i(TAG, "reverseImmersiveAnimation")
        getConvertFragment()?.mConvertManagerImpl?.getConvertViewController()
            ?.reverseImmersiveAnimation()
        immersiveAnimationHelper.reverseImmersiveAnimation()
    }

    /**
     * 任务栏状态变化更新沉浸态布局
     */
    private fun updateImmersiveNavigationHeight(navigationHeight: Int) {
        DebugUtil.i(
            TAG,
            "updateImmersiveNavigationHeight navigationHeight$navigationHeight this.navigationHeight:${this.navigationHeight} windowType:$windowType"
        )
        if (windowType == WindowType.SMALL || this.navigationHeight == null || navigationHeight == 0) {
            this.navigationHeight = navigationHeight
            return
        }
        this.navigationHeight = navigationHeight
        mViewModel?.isImmersiveState?.value?.let {
            immersiveAnimationHelper.updateImmersiveNavigationHeight(navigationHeight, it)
        }
    }

    private fun runOnMain(callback: (() -> Unit)) {
        if (Looper.getMainLooper()?.thread?.id == Thread.currentThread().id) {
            callback.invoke()
        } else {
            lifecycleScope.launch(Dispatchers.Main) {
                callback.invoke()
            }
        }
    }

    private fun fragmentSelected() {
        if (TAB_INDEX_SECOND == currentPosition) {
            (mSummaryFragment as? IAISummaryInterface)?.summaryFragmentSelected()
        } else {
            (mSummaryFragment as? IAISummaryInterface)?.summaryFragmentUnSelected()
        }
    }

    private fun fragmentScrollEnd() {
        val isSummaryPage = TAB_INDEX_SECOND == currentPosition
        (mSummaryFragment as? IAISummaryInterface)?.summaryFragmentScrollEnd(isSummaryPage)
        DebugUtil.i(TAG, "fragmentScrollEnd isSummaryPage = $isSummaryPage")
        if (isSummaryPage) {
            buttonPanelMaybeInVisible()
        } else {
            buttonPanelMaybeVisible()
        }
    }

    private fun fragmentScroll(position: Int, positionOffset: Float) {
        (mSummaryFragment as? IAISummaryInterface)?.summaryFragmentScroll(positionOffset)
        if ((mSummaryFragment as? IAISummaryInterface)?.isSummaryRunning() == true && position == 0) {
            val alpha = 1f - positionOffset
            //滑动过程中必须可见
            if (alpha > 0f) binding.buttonPanel.clButtonPanel.visible()
            binding.buttonPanel.clButtonPanel.alpha = alpha
        }
    }

    private fun buttonPanelMaybeVisible() {
        binding.buttonPanel.clButtonPanel.alpha = 1f
        binding.buttonPanel.clButtonPanel.visible()
    }

    private fun buttonPanelMaybeInVisible() {
        val isSummaryPage = TAB_INDEX_SECOND == currentPosition
        if ((mSummaryFragment as? IAISummaryInterface)?.isSummaryRunning() == true && isSummaryPage) {
            binding.buttonPanel.clButtonPanel.invisible()
        }
    }

    private fun changeMarkListViewDisplayState(isDisplay: Boolean = false) {
        if (isDisplay) {
            showBottomSheetDialogFragment()
        } else {
            dismissBottomSheetDialogFragment()
        }
    }

    private fun showBottomSheetDialogFragment() {
        DebugUtil.i(TAG, "showBottomSheetDialogFragment")
        if (markListDialogFragment?.isAdded == true) {
            DebugUtil.i(TAG, "showBottomSheetDialogFragment already show.")
            return
        }
        mMarkListAdapter?.setIsDialogFragment(true)
        markListDialogFragment?.dismiss()
        markListDialogFragment = MarkListBottomSheetDialogFragment()
        markListDialogFragment?.isHandlePanel = true
        markListDialogFragment?.setIsShowInMaxHeight(true)
        val markListContainerFragment = MarkListContainerFragment(mMarkListAdapter)
        markListDialogFragment?.setMainPanelFragment(markListContainerFragment)
        markListDialogFragment?.setOnDismissListener {
            DebugUtil.e(TAG, "setOnDismissListener")
            if (<EMAIL>) {
                mViewModel?.isShowMarkList?.postValueSafe(false)
            }
            markListDialogFragment?.setOnDismissListener(null)
            markListDialogFragment = null
        }
        markListDialogFragment?.show(childFragmentManager, MARK_LIST_FRAGMENT_TAG)
    }

    private fun dismissBottomSheetDialogFragment() {
        markListDialogFragment?.dismiss()
    }

    fun getFragmentContainerMarginTop(): Int {
        val layoutParams = binding.tabLayout.layoutParams as? ConstraintLayout.LayoutParams ?: return 0
        return layoutParams.height + layoutParams.topMargin + layoutParams.bottomMargin
    }

    fun updateIsImmersive(dy: Int) {
        if (!isInitImmersive) {
            immersiveFirstMoveDownDistance = resources.getDimensionPixelOffset(R.dimen.distance_immersive_first_move_down).toFloat()
            switchImmersiveStateDistance = resources.getDimension(R.dimen.distance_switch_immersive).toInt()
            moveDownDistanceRatio = immersiveFirstMoveDownDistance / switchImmersiveStateDistance
            isInitImmersive = true
        }
        mViewModel?.apply {
            if (isImmersiveAnimationRunning) {
                //沉浸态动画切换会影响列表高度，会引起列表滚动回调，从而会干扰滚动判断，所以动画执行过程中不处理滚动回调
                return
            }
            //是否是沉浸态标记
            val isImmersive = isImmersiveState.value == true
            /* immersiveScrollDistance是列表滚动累加的距离，达到switchImmersiveStateDistance进入沉浸态
            减小到0是退出沉浸态，始终保持 0 <= immersiveScrollDistance <= switchImmersiveStateDistance*/
            if ((dy > 0 && immersiveScrollDistance < switchImmersiveStateDistance) ||
                (dy < 0 && immersiveScrollDistance > 0)
            ) {
                val totalY = immersiveScrollDistance + dy
                immersiveScrollDistance = if (totalY > switchImmersiveStateDistance) {
                    switchImmersiveStateDistance
                } else if (totalY < 0) {
                    0
                } else {
                    totalY
                }
                immersiveMoveDownDistance.value = (immersiveScrollDistance * moveDownDistanceRatio).toInt()
            }
            if (!isImmersive && immersiveScrollDistance >= switchImmersiveStateDistance) {
                // 进入沉浸态
                isImmersiveState.value = true
            } else if (isImmersive && immersiveScrollDistance <= 0) {
                // 退出沉浸态
                isImmersiveState.value = false
            }
        }
    }
}