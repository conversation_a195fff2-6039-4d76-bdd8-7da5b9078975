<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/layout_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <!--使用要根据实际的XML来进行适配-->
        <com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout
            android:id="@+id/animator_speaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:background="@drawable/background_convert_speaker"
            app:max_padding_end="@dimen/dp6">

            <LinearLayout
                android:id="@+id/ll_speaker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_speaker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minHeight="@dimen/dp22"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:includeFontPadding="false"
                    android:lines="1"
                    android:paddingHorizontal="@dimen/dp11"
                    android:maxWidth="@dimen/playback_tv_speaker_maxLength"
                    android:textColor="?attr/couiColorLabelPrimary"
                    android:textFontWeight="500"
                    android:textSize="@dimen/sp10"
                    android:textStyle="normal"
                    android:gravity="center"
                    tools:text="@string/convert_speaker" />

            </LinearLayout>
        </com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout>

        <TextView
            android:id="@+id/start_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription=""
            android:ellipsize="end"
            android:lines="1"
            android:layout_marginStart="@dimen/dp4"
            android:textAppearance="@style/convert_text_time_appearance_not_focused"
            android:fontFeatureSettings="tnum" />
    </LinearLayout>

</LinearLayout>